package com.fxiaoke.bi.paas2bi.transfer.mapper.paas;

import com.fxiaoke.bi.paas2bi.transfer.bean.ObjectLangRecord;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 多语言记录表
 */
@Mapper
public interface MultiLangRecordMapper extends ITenant<MultiLangRecordMapper> {


  /**
   * 查询带有多语言字段的表名
   *
   * @param schemaName 模式名
   * @return 表名列表
   */
  List<String> findTablesWithMultiLangFields(@Param("schemaName") String schemaName);

  /**
   * 查询表中所有记录的ID
   *
   * @param schemaName 模式名
   * @param tableName  表名
   * @return ID列表
   */
  int findAllRecordIds(@Param("schemaName") String schemaName,
                       @Param("tableName") String tableName,
                       @Param("tenantId") String tenantId);

  /**
   * 根据ID查询记录
   *
   * @param schemaName 模式名
   * @param tableName  表名
   * @return 记录Map
   */
  List<ObjectLangRecord> findRecordByTenantId(@Param("schemaName") String schemaName,
                                              @Param("tableName") String tableName,
                                              @Param("tenantId") String tenantId);

  List<ObjectLangRecord> findRecordByApiName(@Param("schemaName") String schemaName,
                                             @Param("tableName") String tableName,
                                             @Param("tenantId") String tenantId,
                                             @Param("apiName") String apiName);

  @SuppressWarnings("MybatisXMapperMethodInspection")
  List<Map<String, Object>> findRecordById(@Param("schemaName") String schemaName,
                                           @Param("tableName") String tableName,
                                           @Param("tenantId") String tenantId,
                                           @Param("primaryKey") String primaryKey);


  /**
   * 查询表结构中的多语言字段
   *
   * @param schemaName 模式名称
   * @param tableName  表名
   * @return 字段名称列表
   */
  List<String> findMultiLangFields(@Param("schemaName") String schemaName, @Param("tableName") String tableName);

  /**
   * 批量根据ID查询记录
   *
   * @param schemaName  模式名
   * @param tableName   表名
   * @param tenantId    租户ID
   * @param primaryKeys 主键列表
   * @return 记录Map列表
   */
  @SuppressWarnings("MybatisXMapperMethodInspection")
  List<Map<String, Object>> findRecordsByIds(@Param("schemaName") String schemaName,
                                             @Param("tableName") String tableName,
                                             @Param("tenantId") String tenantId,
                                             @Param("primaryKeys") List<String> primaryKeys);
}

package com.fxiaoke.bi.paas2bi.transfer.mapper.bi;

import com.fxiaoke.bi.paas2bi.transfer.pojo.UdfObjFieldDO;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface UdfObjFieldMapper extends ITenant<UdfObjFieldMapper> {
  @Select("select uof.ei," +
    "uof.field_name," +
    "uof.db_field_name," +
    "uof.field_location," +
    "uof.db_obj_name," +
    "uof.ref_obj_name," +
    "uof.ref_key_field," +
    "uof.ref_target_field," +
    "uof.url_obj," +
    "uof.is_delete," +
    "uof.relation_key_field," +
    "uof.ui_type," +
    "uof.type," +
    "uof.crm_obj_name," +
    "uof.extend_field_api_name," +
    "uof.relation_table "+
    " from udf_obj_field uof where uof.ei=#{tenantId} and uof.db_obj_name =any(#{dbObjNames}) and uof.is_delete=0 and uof.db_field_name=#{dbFieldName}")
  UdfObjFieldDO findObjFieldByFieldName(@Param("tenantId") int tenantId, @Param("dbObjNames") String[] dbObjNames,@Param("dbFieldName") String dbFieldName);

}

package com.fxiaoke.bi.paas2bi.transfer.consumer;

import com.fxiaoke.bi.paas2bi.transfer.service.PaasDataParseListener;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;

/**
 * Created by jief on 2019/3/19.
 */
@Slf4j
@Service
public class ConsumerMQ implements ApplicationListener<ContextRefreshedEvent> {

  @Resource
  private PaasDataParseListener paasDataParseListener;

  private AutoConfMQPushConsumer consumer;

  @PostConstruct
  public void init() {
    consumer = new AutoConfMQPushConsumer("fs-bi-paas2bi-transfer_goal_mq", this.paasDataParseListener);
  }

  @PreDestroy
  public void destroy() {
    consumer.close();
    log.info("consumer closed!");
  }

  @Override
  public void onApplicationEvent(ContextRefreshedEvent event) {
    if (null == event.getApplicationContext().getParent()) {
      if (null != consumer) {
        consumer.start();
        log.info("consumer started!");
      }
    }
  }
}

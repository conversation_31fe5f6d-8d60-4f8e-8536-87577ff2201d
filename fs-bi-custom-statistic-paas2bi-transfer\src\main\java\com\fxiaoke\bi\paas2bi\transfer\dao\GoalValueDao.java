package com.fxiaoke.bi.paas2bi.transfer.dao;

import com.alibaba.fastjson.JSON;
import com.facishare.bi.common.Constants;
import com.facishare.bi.metadata.context.utils.SysUdfMetadataUtil;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.bi.paas2bi.transfer.bean.*;
import com.fxiaoke.bi.paas2bi.transfer.context.ApplicationContextHolder;
import com.fxiaoke.bi.paas2bi.transfer.mapper.bi.GoalValueHandlerMapper;
import com.fxiaoke.bi.paas2bi.transfer.mapper.bi.ObjectDataMapper;
import com.fxiaoke.bi.paas2bi.transfer.mapper.paas.GoalMapper;
import com.fxiaoke.bi.paas2bi.transfer.pojo.*;
import com.fxiaoke.bi.paas2bi.transfer.service.*;
import com.fxiaoke.bi.paas2bi.transfer.utils.*;
import com.fxiaoke.bi.statistic.common.utils.ObjectConfigManager;
import com.fxiaoke.common.Pair;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.mybatis.util.InjectSchemaUtil;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.jdbc.SQL;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GoalValueDao {

  private final DimSysDateService dimSysDateService;
  private final BIPGDataSource bipgDataSource;
  private final GoalValueHandlerMapper goalValueHandlerMapper;
  private final ObjectDataMapper objectDataMapper;
  private final DimSysAreaCacheService dimSysAreaCacheService;
  private final PaasGoalRuleDao paasGoalRuleDao;
  private final EIEAConverter eieaConverter;
  private final GoalMapper goalMapper;

  public GoalValueDao(DimSysDateService dimSysDateService,
                      BIPGDataSource bipgDataSource,
                      GoalValueHandlerMapper goalValueHandlerMapper,
                      ObjectDataMapper objectDataMapper,
                      DimSysAreaCacheService dimSysAreaCacheService,
                      PaasGoalRuleDao paasGoalRuleDao,
                      @Autowired(required = false) EIEAConverter eieaConverter,
                      GoalMapper goalMapper) {
    this.dimSysDateService = dimSysDateService;
    this.bipgDataSource = bipgDataSource;
    this.goalValueHandlerMapper = goalValueHandlerMapper;
    this.objectDataMapper = objectDataMapper;
    this.dimSysAreaCacheService = dimSysAreaCacheService;
    this.paasGoalRuleDao = paasGoalRuleDao;
    this.eieaConverter = eieaConverter;
    this.goalMapper = goalMapper;
  }

  public static final String DEPARTMENT_OBJ_DEPT_ID_FIELD_ID = "BI_7022110b7a12bd4c22e87d4e970ba4e8";

  private static final String CHECK_LEVEL_UPDATE = "UPDATE ${tableName} \n" +
                                                   "SET last_modified_time = ?, check_level_code_1 = ?, check_level_code_2 = ?, check_level_code_3 = ?, data_auth_id = ?, out_data_auth_id = ?, out_owner = ?, owner = ?, out_tenant_id= ?, data_own_department = ?, data_auth_code = ?, out_data_auth_code = ?, check_dimension_value1 = ?, check_dimension_value2 = ?, check_dimension_fields = ? \n" +
                                                   "WHERE tenant_id = ? AND oid = ? AND check_object_id = ?";

  private static final String DATA_AUTH_CODE_UPDATE = "UPDATE ${tableName} SET last_modified_time = ?, data_auth_code_1 = ?, data_auth_code_2 = ?, data_auth_code_3 = ?, data_auth_code_4 = ?, data_auth_code_5 = ? WHERE tenant_id = ? AND oid = ? AND is_deleted = 0";

  /**
   * 月的值需要加上属于哪个季度，并找出那个季度的值
   */
  private static final String batchInsertMonth =
    "INSERT INTO goal_value_obj (tenant_id,NAME,check_object,goal_rule_id,\n" +
    "goal_rule_detail_id,fiscal_year_month,goal_type,fiscal_year,annual_value,month,\n" +
    "month_value,user_id,dept_id,goal_value_name,OWNER,lock_status,life_status,\n" +
    "record_type,created_by,create_time,last_modified_by,last_modified_time,\n" +
    "extend_obj_data_id,package,object_describe_id,object_describe_api_name,version,\n" +
    "lock_user,lock_rule,life_status_before_invalid,is_deleted,out_tenant_id,\n" +
    "out_owner,data_auth_code,change_type,out_data_auth_code,ID,oid,\n" +
    "check_object_id,data_auth_id,out_data_auth_id,check_level_field_api_name,\n" +
    "check_level_code_1,check_level_code_2,check_level_code_3,\n" +
    "action_date,fiscal_action_date,data_own_department,rule_id,check_dimension_fields,\n" +
    "check_dimension_value1,check_dimension_value2,check_dimension_value3,check_dimension_value4,\n" +
    "check_dimension_value5,check_dimension_value6,check_dimension_value7,check_dimension_value8,\n" +
    "check_dimension_value9,check_dimension_value10,data_auth_code_1,data_auth_code_2,\n" +
    "data_auth_code_3,data_auth_code_4,data_auth_code_5,agg2_annual_value,agg2_month_value,quarter_value, agg2_quarter_value, action_date_quarter) VALUES\n" +
    "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) " +
    "ON CONFLICT(tenant_id, oid, month) DO UPDATE set name=EXCLUDED.name,check_object=EXCLUDED.check_object,\n" +
    "goal_rule_id=EXCLUDED.goal_rule_id,goal_rule_detail_id=EXCLUDED.goal_rule_detail_id,fiscal_year_month=EXCLUDED.fiscal_year_month,\n" +
    "goal_type=EXCLUDED.goal_type,fiscal_year=EXCLUDED.fiscal_year,annual_value=EXCLUDED.annual_value,month_value=EXCLUDED.month_value,user_id=EXCLUDED.user_id,\n" +
    "dept_id=EXCLUDED.dept_id,goal_value_name=EXCLUDED.goal_value_name,owner=EXCLUDED.owner,lock_status=EXCLUDED.lock_status,life_status=EXCLUDED.life_status,\n" +
    "record_type=EXCLUDED.record_type,created_by=EXCLUDED.created_by,last_modified_by=EXCLUDED.last_modified_by,last_modified_time=EXCLUDED.last_modified_time,\n" +
    "extend_obj_data_id=EXCLUDED.extend_obj_data_id,package=EXCLUDED.package,object_describe_id=EXCLUDED.object_describe_id,\n" +
    "object_describe_api_name=EXCLUDED.object_describe_api_name,VERSION=EXCLUDED.VERSION,lock_user=EXCLUDED.lock_user,lock_rule=EXCLUDED.lock_rule,\n" +
    "life_status_before_invalid=EXCLUDED.life_status_before_invalid,is_deleted=EXCLUDED.is_deleted,out_tenant_id=EXCLUDED.out_tenant_id,\n" +
    "out_owner=EXCLUDED.out_owner,data_auth_code=EXCLUDED.data_auth_code,change_type=EXCLUDED.change_type,out_data_auth_code=EXCLUDED.out_data_auth_code,\n" +
    "oid=EXCLUDED.oid,check_object_id=EXCLUDED.check_object_id,data_auth_id=EXCLUDED.data_auth_id,out_data_auth_id=EXCLUDED.out_data_auth_id,\n" +
    "check_level_field_api_name=EXCLUDED.check_level_field_api_name,check_level_code_1=EXCLUDED.check_level_code_1,check_level_code_2=EXCLUDED.check_level_code_2,\n" +
    "check_level_code_3=EXCLUDED.check_level_code_3,action_date=EXCLUDED.action_date,fiscal_action_date=EXCLUDED.fiscal_action_date,data_own_department=EXCLUDED.data_own_department,\n" +
    "rule_id=EXCLUDED.rule_id,check_dimension_fields=EXCLUDED.check_dimension_fields,check_dimension_value1=EXCLUDED.check_dimension_value1,check_dimension_value2=EXCLUDED.check_dimension_value2,\n" +
    "check_dimension_value3=EXCLUDED.check_dimension_value3,check_dimension_value4=EXCLUDED.check_dimension_value4,check_dimension_value5=EXCLUDED.check_dimension_value5,\n" +
    "check_dimension_value6=EXCLUDED.check_dimension_value6,check_dimension_value7=EXCLUDED.check_dimension_value7,check_dimension_value8=EXCLUDED.check_dimension_value8,\n" +
    "check_dimension_value9=EXCLUDED.check_dimension_value9,check_dimension_value10=EXCLUDED.check_dimension_value10,data_auth_code_1=EXCLUDED.data_auth_code_1,\n" +
    "data_auth_code_2=EXCLUDED.data_auth_code_2,data_auth_code_3=EXCLUDED.data_auth_code_3,data_auth_code_4=EXCLUDED.data_auth_code_4,data_auth_code_5=EXCLUDED.data_auth_code_5,\n" +
    "agg2_annual_value=EXCLUDED.agg2_annual_value,agg2_month_value=EXCLUDED.agg2_month_value,quarter_value=EXCLUDED.quarter_value,agg2_quarter_value=EXCLUDED.agg2_quarter_value,action_date_quarter=EXCLUDED.action_date_quarter";

  private static final String batchInsert = "INSERT INTO ${tableName} (tenant_id,name,check_object,goal_rule_id,\n" +
                                            "goal_rule_detail_id,${cycleDateTimestamp},goal_type,fiscal_year,annual_value,${cycleDate},\n" +
                                            "${cycleDateValue},user_id,dept_id,goal_value_name,owner,lock_status,life_status,\n" +
                                            "record_type,created_by,create_time,last_modified_by,last_modified_time,\n" +
                                            "extend_obj_data_id,package,object_describe_id,object_describe_api_name,version,\n" +
                                            "lock_user,lock_rule,life_status_before_invalid,is_deleted,out_tenant_id,\n" +
                                            "out_owner,data_auth_code,change_type,out_data_auth_code,id,oid,\n" +
                                            "check_object_id,data_auth_id,out_data_auth_id,check_level_field_api_name,\n" +
                                            "check_level_code_1,check_level_code_2,check_level_code_3,\n" +
                                            "action_date,fiscal_action_date,data_own_department,rule_id,check_dimension_fields," +
                                            "check_dimension_value1,check_dimension_value2,check_dimension_value3,check_dimension_value4," +
                                            "check_dimension_value5,check_dimension_value6,check_dimension_value7,check_dimension_value8," +
                                            "check_dimension_value9,check_dimension_value10,data_auth_code_1,data_auth_code_2," +
                                            "data_auth_code_3,data_auth_code_4,data_auth_code_5,agg2_annual_value,${agg2CycleDateValue}) " +
                                            "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) " +
                                            "ON CONFLICT(tenant_id, oid, ${cycleDate}) " +
                                            "DO UPDATE set name=EXCLUDED.name,check_object=EXCLUDED.check_object," +
                                            "goal_rule_id=EXCLUDED.goal_rule_id,goal_rule_detail_id=EXCLUDED.goal_rule_detail_id," +
                                            "${cycleDateTimestamp}=EXCLUDED.${cycleDateTimestamp},goal_type=EXCLUDED.goal_type," +
                                            "fiscal_year=EXCLUDED.fiscal_year,annual_value=EXCLUDED.annual_value," +
                                            "${cycleDateValue}=EXCLUDED.${cycleDateValue}," +
                                            "user_id=EXCLUDED.user_id,dept_id=EXCLUDED.dept_id,goal_value_name=EXCLUDED.goal_value_name," +
                                            "owner=EXCLUDED.owner,lock_status=EXCLUDED.lock_status,life_status=EXCLUDED.life_status," +
                                            "record_type=EXCLUDED.record_type,created_by=EXCLUDED.created_by," +
                                            "last_modified_by=EXCLUDED.last_modified_by,last_modified_time=EXCLUDED.last_modified_time," +
                                            "extend_obj_data_id=EXCLUDED.extend_obj_data_id,package=EXCLUDED.package," +
                                            "object_describe_id=EXCLUDED.object_describe_id," +
                                            "object_describe_api_name=EXCLUDED.object_describe_api_name,version=EXCLUDED.version," +
                                            "lock_user=EXCLUDED.lock_user,lock_rule=EXCLUDED.lock_rule," +
                                            "life_status_before_invalid=EXCLUDED.life_status_before_invalid,is_deleted=EXCLUDED.is_deleted," +
                                            "out_tenant_id=EXCLUDED.out_tenant_id,out_owner=EXCLUDED.out_owner," +
                                            "data_auth_code=EXCLUDED.data_auth_code,change_type=EXCLUDED.change_type," +
                                            "out_data_auth_code=EXCLUDED.out_data_auth_code,oid=EXCLUDED.oid," +
                                            "check_object_id=EXCLUDED.check_object_id," +
                                            "data_auth_id=EXCLUDED.data_auth_id," +
                                            "out_data_auth_id=EXCLUDED.out_data_auth_id," +
                                            "check_level_field_api_name=EXCLUDED.check_level_field_api_name," +
                                            "check_level_code_1=EXCLUDED.check_level_code_1,check_level_code_2=EXCLUDED.check_level_code_2," +
                                            "check_level_code_3=EXCLUDED.check_level_code_3,action_date=EXCLUDED.action_date," +
                                            "fiscal_action_date=EXCLUDED.fiscal_action_date,data_own_department=EXCLUDED.data_own_department," +
                                            "rule_id=EXCLUDED.rule_id,check_dimension_fields=EXCLUDED.check_dimension_fields," +
                                            "check_dimension_value1=EXCLUDED.check_dimension_value1,check_dimension_value2=EXCLUDED.check_dimension_value2," +
                                            "check_dimension_value3=EXCLUDED.check_dimension_value3,check_dimension_value4=EXCLUDED.check_dimension_value4," +
                                            "check_dimension_value5=EXCLUDED.check_dimension_value5,check_dimension_value6=EXCLUDED.check_dimension_value6," +
                                            "check_dimension_value7=EXCLUDED.check_dimension_value7,check_dimension_value8=EXCLUDED.check_dimension_value8," +
                                            "check_dimension_value9=EXCLUDED.check_dimension_value9,check_dimension_value10=EXCLUDED.check_dimension_value10," +
                                            "data_auth_code_1=EXCLUDED.data_auth_code_1,data_auth_code_2=EXCLUDED.data_auth_code_2," +
                                            "data_auth_code_3=EXCLUDED.data_auth_code_3,data_auth_code_4=EXCLUDED.data_auth_code_4,data_auth_code_5=EXCLUDED.data_auth_code_5," +
                                            "agg2_annual_value=EXCLUDED.agg2_annual_value,${agg2CycleDateValue}=EXCLUDED.${agg2CycleDateValue}";

  private static final String DELETE = "DELETE FROM ${tableName} WHERE tenant_id = ? AND oid = ?";
  private static final String LOGIC_DELETE = "UPDATE ${tableName} SET is_deleted = -1, last_modified_time = ? WHERE tenant_id = ? AND oid = ?";

  private static final int BATCH_SIZE = 200;

  /**
   * 全量刷新一遍企业的所有goal_value_obj_x 数据
   *
   * @param tenantIds 企业列表
   */
  public void batchEtlGoalValueByTenantIds(List<String> tenantIds) {
    if (CollectionUtils.isNotEmpty(tenantIds)) {
      tenantIds.forEach(tenantId -> {
        List<String> goalRuleIds = goalMapper.setTenantId(tenantId).findAllGoalRuleByTenantId(tenantId);
        if (CollectionUtils.isNotEmpty(goalRuleIds)) {
          goalRuleIds.forEach(goalRuleId -> this.etlGoalValueByGaolRuleId(tenantId, goalRuleId));
        }
      });
    }
  }

  /**
   * 按照企业清洗视图和指标
   *
   * @param tenantId 租户id
   * @return 目标规则的考核周期
   */
  public String etlGoalValueByGaolRuleId(String tenantId, String goalRuleId) {
    CommonsUtils.createTrace(eieaConverter, tenantId, ObjectId.get().toString());
    GoalRuleBO goalRuleBO = goalMapper.setTenantId(tenantId).findCheckCycle(tenantId, goalRuleId);
    if (goalRuleBO == null) {
      //如果gid 为空则表示goal_rule表数据不存在，可能是paas2bi同步延迟，需要反查paas表
      List<PaasGoalRuleDao.GoalRuleCheckLevelDO> paasGoalRules = paasGoalRuleDao.setTenantId(tenantId)
                                                                                .batchQueryPaasGoalRuleByIds(Sets.newHashSet(goalRuleId));
      if (CollectionUtils.isEmpty(paasGoalRules)) {
        log.warn(String.format("can not find goalRuleEntity tenantId:%s,goalRuleId:%s", tenantId, goalRuleId));
        return "month";
      }
      PaasGoalRuleDao.GoalRuleCheckLevelDO goalRuleCheckLevelDO = paasGoalRules.get(0);
      goalRuleBO = new GoalRuleBO();
      goalRuleBO.setId(goalRuleCheckLevelDO.getId());
      goalRuleBO.setTenantId(tenantId);
      goalRuleBO.setName(goalRuleCheckLevelDO.getName());
      goalRuleBO.setCheckCycle(goalRuleCheckLevelDO.getCheckCycle());
      goalRuleBO.setCheckLevelType(goalRuleCheckLevelDO.getCheckLevelType());
      goalRuleBO.setCheckObjectApiName(goalRuleCheckLevelDO.getCheckObjectApiName());
      goalRuleBO.setCheckLevelFieldApiName(goalRuleCheckLevelDO.getCheckLevelFieldApiNames());
    }
    String checkCycle = goalRuleBO.getCheckCycle();
    //rule更新 如果从paas获取财年财月的话则直接跳过计算
    if (GrayManagerUtil.fiscalFromPaas(tenantId) && Objects.equals(checkCycle, "month")) {
      return "month";
    }
    Map<String, String> sqlTemplateParams = goalRuleBO.findTableAndFieldMap();
    try (JdbcConnection jdbcConnection = bipgDataSource.getJdbcConnectionByTenantID(tenantId)) {
      try {
        Connection connection = jdbcConnection.connection();
        boolean isSchema = bipgDataSource.isSchema(tenantId);
        PageInfo<GoalValuePlus> pageInfo = new PageInfo<>(null, "0", 50);
        FxTemplate.replace(batchInsert, sqlTemplateParams);
        String sql;
        if (Objects.isNull(checkCycle) || Objects.equals(checkCycle, "month")) {
          sql = batchInsertMonth;
        } else {
          sql = FxTemplate.replace(batchInsert, sqlTemplateParams);
        }
        if (isSchema) {
          sql = InjectSchemaUtil.injectSchema(sql, "postgresql", "sch_" + tenantId);
        }
        GoalValueObjStatementExecutor upsertExecutor = new GoalValueObjStatementExecutor("upsert_goal", connection, BATCH_SIZE, sql);
        GoalValueDeleteStatementExecutor deleteExecutor = new GoalValueDeleteStatementExecutor("delete_gaol", connection, BATCH_SIZE, DELETE, tenantId, isSchema);
        do {
          List<GoalValuePlus> goalValuePluses = goalMapper.setTenantId(tenantId)
                                                          .batchQueryGoalValueByGoalRuleId(tenantId, pageInfo.getLastFlag(), goalRuleId, pageInfo.getPageSize());
          pageInfo.setQueryList(goalValuePluses);
          this.upsertGoalValueObj(tenantId, pageInfo, upsertExecutor, deleteExecutor, isSchema, null);
          log.info("success etl batch tenant:{},size{}", tenantId, pageInfo.queryListSize());
        } while (!pageInfo.isLastPage());
      } catch (Exception e) {
        log.error("etl goal_value_obj error tenantId:{},goalRuleId:{}", tenantId, goalRuleId, e);
        throw new RuntimeException(e);
      }
    } catch (Exception e) {
      log.error("jdbc connection close error", e);
    }
    return checkCycle;
  }

  /**
   * upsert 目标值
   *
   * @param tenantId 租户id
   * @param id       goal_value_id
   */
  public void upsertGoalValueObj(String tenantId, String id) {
    CommonsUtils.createTrace(eieaConverter, tenantId, ObjectId.get().toString());
    List<GoalValuePlus> goalValuePlusList = goalMapper.setTenantId(tenantId).queryGoalValueById(tenantId, id);
    if (!CollectionUtils.isEmpty(goalValuePlusList)) {
      if (goalValuePlusList.size() > 1) {
        log.error("find more then one goalValueEntity tenantId:{},goalValueId:{}", tenantId, id);
      }
      GoalValuePlus goalValuePlus = goalValuePlusList.get(0);
      if (StringUtils.isBlank(goalValuePlus.getGid())) {
        //如果gid 为空则表示goal_rule表数据不存在，可能是paas2bi同步延迟，需要反查paas表
        List<PaasGoalRuleDao.GoalRuleCheckLevelDO> paasGoalRules = paasGoalRuleDao.setTenantId(tenantId)
                                                                                  .batchQueryPaasGoalRuleByIds(Sets.newHashSet(goalValuePlus.getGoalRuleId()));
        if (CollectionUtils.isEmpty(paasGoalRules)) {
          log.warn("can not find goalRuleEntity tenantId:{},goalRuleId:{}", tenantId, goalValuePlus.getGoalRuleId());
          return;
        }
        PaasGoalRuleDao.GoalRuleCheckLevelDO goalRuleCheckLevelDO = paasGoalRules.get(0);
        goalValuePlus.setGid(goalValuePlus.getGoalRuleId());
        goalValuePlus.setStartMonth(goalRuleCheckLevelDO.getStartMonth());
        goalValuePlus.setStartQuarter(goalRuleCheckLevelDO.getStartQuarter());
        goalValuePlus.setStartWeek(goalRuleCheckLevelDO.getStartWeek());
        goalValuePlus.setCheckCycle(goalRuleCheckLevelDO.getCheckCycle());
        goalValuePlus.setCheckLevelType(goalRuleCheckLevelDO.getCheckLevelType());
        goalValuePlus.setCheckLevelFieldApiNames(goalRuleCheckLevelDO.getCheckLevelFieldApiNames());
      }
      try (JdbcConnection jdbcConnection = bipgDataSource.getJdbcConnectionByTenantID(tenantId)) {
        Connection connection = jdbcConnection.connection();
        boolean isSchema = bipgDataSource.isSchema(tenantId);
        String sql;
        String checkCycle = goalValuePlus.getCheckCycle();
        if (Objects.isNull(checkCycle) || Objects.equals(checkCycle, "month")) {
          sql = batchInsertMonth;
        } else {
          sql = FxTemplate.replace(batchInsert, goalValuePlus.findTableAndFieldMap());
        }
        if (isSchema) {
          sql = InjectSchemaUtil.injectSchema(sql, "postgresql", "sch_" + tenantId);
        }
        GoalValueObjStatementExecutor upsertExecutor = new GoalValueObjStatementExecutor("upsert_goal", connection, BATCH_SIZE, sql);
        GoalValueDeleteStatementExecutor deleteExecutor = new GoalValueDeleteStatementExecutor("delete_gaol", connection, BATCH_SIZE, DELETE, tenantId, isSchema);
        PageInfo<GoalValuePlus> pageInfo = new PageInfo<>(goalValuePlusList, "0", 50);
        this.upsertGoalValueObj(tenantId, pageInfo, upsertExecutor, deleteExecutor, isSchema, checkCycle);
      } catch (Exception e) {
        log.error("etl goalValue error tenantId:{},id:{}", tenantId, id, e);
      }
    } else {
      log.warn("can not find goalValueEntity tenantId:{},goalValueId:{}", tenantId, id);
    }
  }

  /**
   * 入库操作
   *
   * @param tenantId       租户id
   * @param pageInfo       分页数据
   * @param upsertExecutor 执行批量操作
   * @throws Exception 插入或更新出现异常
   */
  public void upsertGoalValueObj(String tenantId,
                                 PageInfo<GoalValuePlus> pageInfo,
                                 GoalValueObjStatementExecutor upsertExecutor,
                                 GoalValueDeleteStatementExecutor deleteExecutor,
                                 boolean isSchema,
                                 String checkCycle) throws Exception {
    List<GoalValuePlus> goalValuePlusList = pageInfo.getQueryList();
    if (CollectionUtils.isEmpty(goalValuePlusList)) {
      return;
    }
    // goalValue更新时判断 是否走paas财年计算，只有月
    boolean isPaas = GrayManagerUtil.fiscalFromPaas(tenantId) && Objects.equals(checkCycle, "month");
    String currentId = null;
    // 根据是否有值分组， 无(true)：删除原来的；有(false)：进行更新或插入
    Map<Boolean, List<GoalValuePlus>> deleteOrUpsert = goalValuePlusList.stream()
                                                                        .collect(Collectors.groupingBy(this::haveNoGoalValue));
    List<GoalValuePlus> deleteList = deleteOrUpsert.get(true);
    if (CollectionUtils.isNotEmpty(deleteList)) {
      deleteList.forEach(deleteExecutor);
    }
    List<GoalValuePlus> upsertList = deleteOrUpsert.get(false);
    if (CollectionUtils.isNotEmpty(upsertList)) {
      currentId = etlGoalValue(upsertList, tenantId, isSchema, isPaas, upsertExecutor);
    }
    upsertExecutor.flush();
    deleteExecutor.flush();
    pageInfo.setLastFlag(currentId);
  }

  /**
   * 批量处理目标值
   */
  private String etlGoalValue(List<GoalValuePlus> goalValues,
                              String tenantId,
                              boolean isSchema,
                              boolean isPaas,
                              GoalValueObjStatementExecutor upsertExecutor) throws Exception {
    String currentId = null;
    for (GoalValuePlus goalValuePlus : goalValues) {
      this.createCheckLevel(tenantId, goalValuePlus, isSchema);
      List<DimSysDate> dimSysDates = dimSysDateService.getDimSysDate(tenantId, goalValuePlus);
      List<GoalValueObjCommon> goalValueObjCommons;
      if (isPaas) {
        List<GoalValueObj> paasGoalValues = getNewGoalValueObjArgList(goalValuePlus);
        // 无法从paas获取时
        if (CollectionUtils.isEmpty(paasGoalValues)) {
          log.error("Failed to get fiscal year from paas side!, tenantId: {}, goalValueId: {}", tenantId, goalValuePlus.getId());
          continue;
        }
        goalValueObjCommons = copyToCommonList(paasGoalValues);
      } else {
        String ruleCheckDimensionFields = goalValuePlus.getRuleCheckDimensionFields();
        List<CheckDimensionField> checkDimensionFields = JSON.parseArray(ruleCheckDimensionFields, CheckDimensionField.class);
        this.createMultiGoalDataAuthCode(tenantId, goalValuePlus, isSchema, checkDimensionFields);
        goalValueObjCommons = goalValuePlus.transformToGoalValueObj(dimSysDates);
        this.etlOldGoalToMultiGoal(tenantId, goalValuePlus.getGoalRuleId(), goalValuePlus.getGoalRuleDetailId(), goalValuePlus.getCheckLevelFieldApiNames(), goalValueObjCommons);
      }
      goalValueObjCommons.forEach(upsertExecutor);
      currentId = goalValuePlus.getId();
    }
    return currentId;
  }

  /**
   * 考核层级更新
   *
   * @param tenantId 租户id
   * @param pageInfo 待更新的goal_value
   */
  public void updateCheckLevel(String tenantId, PageInfo<GoalValuePlus> pageInfo, boolean isSchema) {
    CommonsUtils.createTrace(eieaConverter, tenantId, ObjectId.get().toString());
    List<GoalValuePlus> goalValuePlusList = pageInfo.getQueryList();
    if (CollectionUtils.isEmpty(goalValuePlusList)) {
      return;
    }
    try (JdbcConnection jdbcConnection = bipgDataSource.getJdbcConnectionByTenantID(tenantId)) {
      GoalValueCheckLevelStatementExecutor executor = new GoalValueCheckLevelStatementExecutor("update_check_level", jdbcConnection.connection(), BATCH_SIZE, CHECK_LEVEL_UPDATE, tenantId, isSchema);
      goalValuePlusList.forEach(item -> {
        this.createCheckLevel(tenantId, item, isSchema);
        this.etlOldGoalToMultiGoal(tenantId, item);
        executor.accept(item);
      });
      executor.flush();
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * 将老目标的check_dimension_value补全
   */
  public void etlOldGoalToMultiGoal(String tenantId,
                                    String goalRuleId,
                                    String goalRuleDetailId,
                                    String ruleCheckLevelFieldApiNames,
                                    List<GoalValueObjCommon> objCommonList) {
    if (StringUtils.isBlank(ruleCheckLevelFieldApiNames)) {
      return;
    }
    String[] checkLevelFieldApiNameArr = StringUtils.split(ruleCheckLevelFieldApiNames, '|');
    String realRuleId = goalRuleId;
    if (StringUtils.isNotBlank(goalRuleDetailId)) {
      realRuleId = goalRuleId + "|" + goalRuleDetailId;
    }
    List<BiMtDimensionDO> dimensionIdList = objectDataMapper.setTenantId(tenantId)
                                                            .getDimensionIdByRuleId(tenantId, realRuleId);
    // goal_rule checkLevelFieldApiName 为单个字段时（不包含 | ）
    if (checkLevelFieldApiNameArr.length == 1) {
      objCommonList.forEach(objCommon -> {
        objCommon.setCheckDimensionValue1(objCommon.getCheckObjectId());
        objCommon.setCheckDimensionFields(dimensionIdList.stream()
                                                         .map(BiMtDimensionDO::getFieldId)
                                                         .toArray(String[]::new));
      });
    } else if (checkLevelFieldApiNameArr.length == 2) {
      for (GoalValueObjCommon goalValueObjCommon : objCommonList) {
        String valueCheckLevelFieldApiName = goalValueObjCommon.getCheckFieldApiName();
        if (Objects.equals(valueCheckLevelFieldApiName, checkLevelFieldApiNameArr[0])) {
          objCommonList.forEach(objCommon -> {
            objCommon.setCheckDimensionValue1(objCommon.getCheckObjectId());
            String[] array;
            if ("main_department".equals(valueCheckLevelFieldApiName)) {
              array = new String[] {DEPARTMENT_OBJ_DEPT_ID_FIELD_ID};
            } else {
              array = dimensionIdList.stream()
                                     .filter(dimension -> dimension.getDimensionField()
                                                                   .contains(valueCheckLevelFieldApiName))
                                     .map(BiMtDimensionDO::getFieldId)
                                     .toArray(String[]::new);
            }
            objCommon.setCheckDimensionFields(array);
          });
        } else {
          objCommonList.forEach(objCommon -> {
            objCommon.setCheckDimensionValue1(objCommon.getCheckLevel1Code());
            objCommon.setCheckDimensionValue2(objCommon.getCheckObjectId());
            objCommon.setCheckDimensionFields(dimensionIdList.stream()
                                                             .map(BiMtDimensionDO::getFieldId)
                                                             .toArray(String[]::new));
          });
        }
      }
    }
  }

  private void etlOldGoalToMultiGoal(String tenantId, GoalValuePlus goalValuePlus) {
    String goalRuleId = goalValuePlus.getGoalRuleId();
    String goalRuleDetailId = goalValuePlus.getGoalRuleDetailId();
    String ruleCheckLevelFieldApiNames = goalValuePlus.getCheckLevelFieldApiNames();
    if (StringUtils.isBlank(ruleCheckLevelFieldApiNames)) {
      return;
    }
    String[] checkLevelFieldApiNameArr = StringUtils.split(ruleCheckLevelFieldApiNames, '|');
    String realRuleId = goalRuleId;
    if (StringUtils.isNotBlank(goalRuleDetailId)) {
      realRuleId = goalRuleId + "|" + goalRuleDetailId;
    }
    List<BiMtDimensionDO> dimensionIdList = objectDataMapper.setTenantId(tenantId)
                                                            .getDimensionIdByRuleId(tenantId, realRuleId);
    if (checkLevelFieldApiNameArr.length == 1) {
      goalValuePlus.setCheckDimensionValue1(goalValuePlus.getCheckObjectId());
      goalValuePlus.setCheckDimensionFields(JSON.toJSONString(dimensionIdList.stream()
                                                                             .map(BiMtDimensionDO::getFieldId)
                                                                             .toArray(String[]::new)));
    } else if (checkLevelFieldApiNameArr.length == 2) {
      String valueCheckLevelFieldApiName = goalValuePlus.getCheckLevelFieldApiName();
      if (Objects.equals(valueCheckLevelFieldApiName, checkLevelFieldApiNameArr[0])) {
        goalValuePlus.setCheckDimensionValue1(goalValuePlus.getCheckObjectId());
        String[] array;
        if ("main_department".equals(valueCheckLevelFieldApiName)) {
          array = new String[] {DEPARTMENT_OBJ_DEPT_ID_FIELD_ID};
        } else {
          array = dimensionIdList.stream()
                                 .filter(dimension -> dimension.getDimensionField()
                                                               .contains(valueCheckLevelFieldApiName))
                                 .map(BiMtDimensionDO::getFieldId)
                                 .toArray(String[]::new);
        }
        goalValuePlus.setCheckDimensionFields(JSON.toJSONString(array));
      } else {
        goalValuePlus.setCheckDimensionValue1(goalValuePlus.getCheckLevel1Code());
        goalValuePlus.setCheckDimensionValue2(goalValuePlus.getCheckObjectId());
        goalValuePlus.setCheckDimensionFields(JSON.toJSONString(dimensionIdList.stream()
                                                                               .map(BiMtDimensionDO::getFieldId)
                                                                               .toArray(String[]::new)));
      }
    }
  }

  /**
   * 根据考核周期判断目标值是否为空
   *
   * @param goalValuePlus 目标规则和目标值信息
   * @return 月，周，季度是否为空
   */
  private boolean haveNoGoalValue(GoalValuePlus goalValuePlus) {
    String checkCycle = goalValuePlus.getCheckCycle();
    if (StringUtils.isBlank(checkCycle)) {
      log.warn("check cycle is null {}:{}:{}", goalValuePlus.getTenantId(), goalValuePlus.getId(), goalValuePlus.getGoalRuleId());
      goalValuePlus.setCheckCycle("month");
      checkCycle = "month";
    }
    switch (checkCycle) {
      case "month": {
        return goalValuePlus.haveNoMonthValues();
      }
      case "week": {
        try {
          return goalValuePlus.haveNoWeekValues();
        } catch (Exception e) {
          log.error("error when judging if there is {} data!", checkCycle, e);
          throw new RuntimeException(e);
        }
      }
      case "quarter": {
        try {
          return goalValuePlus.haveNoQuarterValues();
        } catch (Exception e) {
          log.error("error when judging if there is {} data!", checkCycle, e);
          throw new RuntimeException(e);
        }
      }
      default: {
        return false;
      }
    }
  }

  /**
   * 处理多维目标字段
   */
  public void updateMultiDimGoalFields(String tenantId,
                                       PageInfo<GoalValuePlus> pageInfo,
                                       Boolean isSchema,
                                       List<CheckDimensionField> checkDimensionFieldList) {
    CommonsUtils.createTrace(eieaConverter, tenantId, ObjectId.get().toString());
    List<GoalValuePlus> goalValueList = pageInfo.getQueryList();
    if (CollectionUtils.isEmpty(goalValueList)) {
      return;
    }
    JdbcConnection jdbcConnection = bipgDataSource.getJdbcConnectionByTenantID(tenantId);
    try (Connection connection = jdbcConnection.connection()) {
      MultiGoalStatementExecutor executor = new MultiGoalStatementExecutor("update_multi_goal", connection, 2, DATA_AUTH_CODE_UPDATE, tenantId, isSchema);
      goalValueList.forEach(goalValuePlus -> {
        this.createMultiGoalDataAuthCode(tenantId, goalValuePlus, isSchema, checkDimensionFieldList);
        if (goalValuePlus.needUpdateAuth()) {
          executor.accept(goalValuePlus);
        }
      });
      executor.flush();
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * 有 auth_code_field_location 的清洗 data_auth_code
   */
  public void createMultiGoalDataAuthCode(String tenantId,
                                          GoalValuePlus goalValuePlus,
                                          Boolean isSchema,
                                          List<CheckDimensionField> checkDimensionFieldList) {
    HashMap<String, String> locationCodeMap = new HashMap<>();
    ArrayList<String> subSqlList = new ArrayList<>();
    if (CollectionUtils.isEmpty(checkDimensionFieldList)) {
      return;
    }
    for (CheckDimensionField checkDimensionField : checkDimensionFieldList) {
      String authCodeFieldLocation = checkDimensionField.getAuthCodeFieldLocation();
      if (StringUtils.isBlank(authCodeFieldLocation)) {
        continue;
      }
      String fieldLocation = checkDimensionField.getFieldLocation();
      String checkDimensionValue = goalValuePlus.findCheckDimensionValue(fieldLocation);
      if (Objects.isNull(checkDimensionValue)) {
        continue;
      }
      Pair<String, String> tableColumn = checkDimensionField.spellSchema(isSchema, tenantId);
      String sqlStr = new SQL().SELECT("'" + authCodeFieldLocation + "' AS auth_code_field_location, data_auth_code")
                               .FROM(tableColumn.first)
                               .WHERE("tenant_id = '" + tenantId + "' AND " + tableColumn.second + " = '" +
                                      checkDimensionValue + "'")
                               .toString();
      subSqlList.add(sqlStr);
    }
    String sql = String.join(" UNION ALL ", subSqlList);
    if (StringUtils.isBlank(sql)) {
      return;
    }
    List<CheckDimensionField> dataAuthCodeList = objectDataMapper.setTenantId(tenantId).getDataAuthCodeListById(sql);
    if (CollectionUtils.isNotEmpty(dataAuthCodeList)) {
      dataAuthCodeList.forEach(checkValue -> locationCodeMap.put(checkValue.getAuthCodeFieldLocation(), checkValue.getDataAuthCode()));
    }
    goalValuePlus.setDataAuthCode1(locationCodeMap.get("data_auth_code_1"));
    goalValuePlus.setDataAuthCode2(locationCodeMap.get("data_auth_code_2"));
    goalValuePlus.setDataAuthCode3(locationCodeMap.get("data_auth_code_3"));
    goalValuePlus.setDataAuthCode4(locationCodeMap.get("data_auth_code_4"));
    goalValuePlus.setDataAuthCode5(locationCodeMap.get("data_auth_code_5"));
  }

  /**
   * //清洗checkLevel type 扩展一些维度字段
   *
   * @param tenantId      企业id
   * @param goalValuePlus goalValueObj
   * @param isSchema      是否schema隔离
   */
  public void createCheckLevel(String tenantId, final GoalValuePlus goalValuePlus, boolean isSchema) {
    String goalType = goalValuePlus.getGoalType();
    if (Context.MULTI_DIM_GOAL.equals(goalType)) {
      return;
    }
    Map<String, Object> map = Maps.newHashMap();
    // goal_type=1 是考核部门
    String objectDescribeApiName = StringUtils.equalsAny(goalType, "1", "2") ?
      "org_employee_user" :
      SysUdfMetadataUtil.getBiObjNameByCrmObjName(goalType);
    String type = goalValuePlus.getCheckLevelType();
    String checkObjectId = goalValuePlus.getCheckObjectId();
    String checkLevelFieldApiNam = goalValuePlus.getCheckLevelFieldApiName();
    String checkLevelFieldApiNames = goalValuePlus.getCheckLevelFieldApiNames();
    String[] checkLevelFieldApiNameArray = StringUtils.split(checkLevelFieldApiNames, "\\|");
    if (StringUtils.isNotBlank(checkLevelFieldApiNam) && Objects.nonNull(checkLevelFieldApiNameArray)) {
      for (int i = 0; i < checkLevelFieldApiNameArray.length; i++) {
        String fieldLocationApiName = checkLevelFieldApiNameArray[0];
        switch (type) {
          case "6":
            if (checkLevelFieldApiNam.equals(checkLevelFieldApiNameArray[i])) {
              if (i == 0) {//表示的是省
                map.put("check_level_code_" + (i + 1), checkObjectId);
                break;
              } else {//表示的是市
                map = dimSysAreaCacheService.findCityData(checkObjectId);
              }
            }
            break;
          case "5":
            map.put("check_level_code_" + (i + 1), checkObjectId);
            break;
          case "4":
            if (checkLevelFieldApiNam.equals(checkLevelFieldApiNameArray[i])) {
              if (i == 0) {//表示层级
                map.put("check_level_code_" + (i + 1), checkObjectId);
              } else {//表示明细 明细的层级是通过对象本身去关联词查询
                Map<String, String> fieldLocationMap = goalValueHandlerMapper.setTenantId(tenantId)
                                                                             .getFiledLocation(Integer.parseInt(tenantId), objectDescribeApiName, ObjectConfigManager.getExtendObjName(objectDescribeApiName), fieldLocationApiName);
                String fieldLocation = fieldLocationMap.get("field_location");
                if (Integer.parseInt(fieldLocation) > -1) {
                  fieldLocationApiName = "value" + fieldLocation;
                }
                map = goalValueHandlerMapper.setTenantId(tenantId)
                                            .getFieldValuePlus(tenantId, fieldLocationApiName, objectDescribeApiName, checkObjectId, isSchema);
              }
            }
            break;
          case "3":
            if (checkLevelFieldApiNam.equals(checkLevelFieldApiNameArray[i])) {
              if (i == 0) {//表示主属部门
                map.put("check_level_code_" + (i + 1), checkObjectId);
              } else if (i == 1) {//表示人员
                map = goalValueHandlerMapper.setTenantId(tenantId).getUserId(tenantId, checkObjectId);
              } else {//表示 明细信息
                map = goalValueHandlerMapper.setTenantId(tenantId)
                                            .getObjectDetailPlus(tenantId, checkObjectId, objectDescribeApiName, isSchema);
              }
            }
            break;
          case "2":
            map = goalValueHandlerMapper.setTenantId(tenantId)
                                        .getObjectDetailPlus(tenantId, checkObjectId, objectDescribeApiName, isSchema);
            break;
          case "1":
            if (checkLevelFieldApiNam.equals(checkLevelFieldApiNameArray[i])) {
              if (i == 0) {//表示是部门
                map.put("check_level_code_" + (i + 1), checkObjectId);
              } else {//表示是人员
                map = goalValueHandlerMapper.setTenantId(tenantId).getUserId(tenantId, checkObjectId);
              }
            }
            break;
        }
      }
    }
    if (map == null) {
      map = Maps.newHashMap();
    }
    goalValuePlus.setCheckLevel1Code(getStringValue("check_level_code_1", map));
    goalValuePlus.setCheckLevel2Code(getStringValue("check_level_code_2", map));
    goalValuePlus.setCheckLevel3Code(getStringValue("check_level_code_3", map));
    goalValuePlus.setDataAuthId((Integer) map.getOrDefault("data_auth_id", -1));
    goalValuePlus.setOutDataAuthId((Integer) map.getOrDefault("out_data_auth_id", -1));
    goalValuePlus.setOutOwner(getStringValue("out_owner", map));
    goalValuePlus.setOwner(getStringValue("owner", map));
    goalValuePlus.setOutTenantId(getStringValue("out_tenant_id", map));
    goalValuePlus.setDataOwnDepartment(getStringValue("data_own_department", map));
    goalValuePlus.setDataAuthCode(getStringValue("data_auth_code", map));
    goalValuePlus.setOutDataAuthCode(getStringValue("out_data_auth_code", map));
  }


  public String getStringValue(final String key, final Map<String, Object> map) {
    if (map == null) {
      return null;
    }
    Object value = map.getOrDefault(key, null);
    return value == null ? null : String.valueOf(value);
  }

  private void deleteGoalValue(Map<String, String> tableNameMap, String tenantId, String oid) {
    String sql = FxTemplate.replace(DELETE, tableNameMap);
    // 如果是灰度ch企业改成逻辑删除，需要让数据能同步到ch，不能物理删除
    if (GrayManagerUtil.isAllow("logicDelGoalValue", tenantId)) {
      sql = FxTemplate.replace(LOGIC_DELETE, tableNameMap);
    }
    // 判断是否隔离
    Boolean standalone = bipgDataSource.isSchema(tenantId);
    if (BooleanUtils.isTrue(standalone)) {
      sql = InjectSchemaUtil.injectSchema(sql, "postgresql", "sch_" + tenantId);
    }
    try (JdbcConnection connection = bipgDataSource.getJdbcConnectionByTenantID(tenantId)) {
      try {
        int count = connection.prepareUpdate2(sql, preparedStatement -> {
          preparedStatement.setLong(1, new Date().getTime());
          preparedStatement.setString(2, tenantId);
          preparedStatement.setString(3, oid);
        });
        // delete from goal_value_obj, count
        log.info("delete from {}, count {}, oid:{}", tableNameMap.get("tableName"), count, oid);
      } catch (Exception e) {
        log.error("db close error.", e);
      }
    } catch (Exception e) {
      log.error("db close error.", e);
    }
  }

  /**
   * 判断月、周、季度、年表中是否有数据
   *
   * @param tenantId 租户id
   * @param oid      goal_value_id
   */
  public void deleteGoalValue(String tenantId, String oid) {
    String tableName = "tableName";
    Map<String, String> map = new HashMap<>();
    map.put(tableName, "goal_value_obj");
    deleteGoalValue(map, tenantId, oid);
    map.put(tableName, "goal_value_obj_week");
    deleteGoalValue(map, tenantId, oid);
    map.put(tableName, "goal_value_obj_quarter");
    deleteGoalValue(map, tenantId, oid);
    map.put(tableName, "goal_value_obj_year");
    deleteGoalValue(map, tenantId, oid);
  }

  /**
   * 通过paas 财年设置计算的goal_value_obj 数据
   *
   * @param goalValueOb goal_value_obj
   * @return 通过paas财年计算得到的月目标值数据
   */
  public static List<GoalValueObj> getNewGoalValueObjArgList(GoalValuePlus goalValueOb) {
    String tenantId = goalValueOb.getTenantId();
    String goalType = goalValueOb.getGoalType();
    List<GoalValueObjInfo> newGoalValueObjInfos = null;
    if (GrayManagerUtil.fiscalFromPaas(tenantId)) {
      FiscalChangeService fiscalChangeService = ApplicationContextHolder.getBean(FiscalChangeService.class);
      //      log.info("goalvalue teanntId:{},data:{}",tenantId, JSON.toJSONString(goalValueOb));
      FiscalYear fiscalYear = fiscalChangeService.findFiscalConfig(tenantId, goalValueOb.getFiscalYear());
      //      log.info("goalvalue teanntId:{},fiscalYear:{}",tenantId, JSON.toJSONString(fiscalYear));
      if (fiscalYear != null) {
        GoalValueEntity goalValueEntity = new GoalValueEntity();
        BeanUtils.copyProperties(goalValueOb, goalValueEntity);
        newGoalValueObjInfos = fiscalChangeService.createNewGoalValueObj(goalValueEntity, fiscalYear);
      }
    }
    if (CollectionUtils.isEmpty(newGoalValueObjInfos)) {
      log.error("can not get fiscal year info from paas tenantId:{},goalRuleId:{},goalRuleDetail:{}", tenantId, goalValueOb.getGoalRuleId(), goalValueOb.getGoalRuleDetailId());
      return null;
    }
    //    log.info("goalvalue teanntId:{},newGoalValueObjInfos:{}",tenantId, JSON.toJSONString(newGoalValueObjInfos));
    List<GoalValueObj> goalValueObjArgs = new ArrayList<>();
    for (GoalValueObjInfo goalValueObjInfo : newGoalValueObjInfos) {
      String goalValueName;
      GoalValueObj goalValueObj = new GoalValueObj();
      goalValueObj.setTenant_id(goalValueOb.getTenantId());
      goalValueObj.setName(goalValueOb.getName());
      if (StringUtils.equalsAny(goalType, "1", "2")) {
        goalValueObj.setCheck_object("org_employee_user");
      } else {
        if (Context.MULTI_DIM_GOAL.equals(goalType)) {
          goalValueObj.setCheck_object(Context.MULTI_DIM_GOAL);
        } else {
          goalValueObj.setCheck_object(SysUdfMetadataUtil.getBiObjNameByCrmObjName(goalType));
        }
      }
      goalValueObj.setGoal_rule_id(goalValueOb.getGoalRuleId());
      goalValueObj.setRule_id(goalValueOb.getGoalRuleId());
      if (StringUtils.isBlank(goalValueOb.getGoalRuleDetailId())) {
        goalValueObj.setGoal_rule_detail_id("nogoaldetail");
        goalValueName =
          goalValueOb.getCheckObjectId() + "_" + goalType + "_" + goalValueOb.getGoalRuleId() + "_nogoaldetail";
      } else {
        goalValueName = goalValueOb.getCheckObjectId() + "_" + goalType + "_" + goalValueOb.getGoalRuleId() + "_" +
                        goalValueOb.getGoalRuleDetailId();
        goalValueObj.setGoal_rule_detail_id(goalValueOb.getGoalRuleDetailId());
        goalValueObj.setRule_id(goalValueOb.getGoalRuleDetailId());
      }
      goalValueObj.setFiscal_year_month(goalValueObjInfo.getFiscalYearMonth());
      goalValueObj.setAction_date(goalValueObjInfo.getActionDate());
      goalValueObj.setFiscal_action_date(goalValueObjInfo.getFiscalActionDate());
      goalValueObj.setFiscal_year(goalValueObjInfo.getFiscalYear());
      goalValueObj.setMonth(goalValueObjInfo.getMonth());
      goalValueObj.setMonth_value(goalValueObjInfo.getMonthValue());
      goalValueObj.setAnnual_value(goalValueOb.getAnnualValue());
      goalValueObj.setGoal_type(goalType);
      if (goalType.equals("2")) {
        goalValueObj.setUser_id(goalValueOb.getCheckObjectId());
      } else {
        goalValueObj.setUser_id(null);
      }
      if (goalType.equals("1")) {
        goalValueObj.setDept_id(goalValueOb.getCheckObjectId());
      } else {
        goalValueObj.setDept_id(null);
      }
      goalValueObj.setGoal_value_name(goalValueName);
      goalValueObj.setOwner(goalValueOb.getOwner());
      goalValueObj.setLock_status(goalValueOb.getLockStatus());
      goalValueObj.setLife_status(goalValueOb.getLifeStatus());
      goalValueObj.setRecord_type(goalValueOb.getRecordType());
      goalValueObj.setCreated_by(goalValueOb.getCreatedBy());
      goalValueObj.setCreate_time(goalValueOb.getCreateTime());
      goalValueObj.setLast_modified_by(goalValueOb.getLastModifiedBy());
      goalValueObj.setLast_modified_time(goalValueOb.getLastModifiedTime());
      goalValueObj.setExtend_obj_data_id(goalValueOb.getExtendObjDataId());
      goalValueObj.setPackage_name(goalValueOb.getPackageName());
      goalValueObj.setObject_describe_id(goalValueOb.getObjectDescribeId());
      goalValueObj.setObject_describe_api_name(goalValueOb.getObjectDescribeApiName());
      goalValueObj.setVersion(goalValueOb.getVersion());
      goalValueObj.setLock_user(goalValueOb.getLockUser());
      goalValueObj.setLock_rule(goalValueOb.getLockRule());
      goalValueObj.setLife_status_before_invalid(goalValueOb.getLifeStatusBeforeInvalid());
      goalValueObj.setIs_deleted(goalValueOb.getIsDeleted());
      goalValueObj.setOut_tenant_id(goalValueOb.getOutTenantId());
      goalValueObj.setOut_owner(goalValueOb.getOutOwner());
      goalValueObj.setData_auth_code(goalValueOb.getDataAuthCode());
      goalValueObj.setChange_type(goalValueOb.getChangeType());
      goalValueObj.setOut_data_auth_code(goalValueOb.getOutDataAuthCode());
      //这里的ID是自动生成的
      goalValueObj.setId(UUID.randomUUID().toString().replace("-", ""));
      goalValueObj.setOid(goalValueOb.getId());
      goalValueObj.setCheck_object_id(goalValueOb.getCheckObjectId());
      goalValueObj.setCheck_field_api_name(goalValueOb.getCheckLevelFieldApiName());
      goalValueObj.setCheck_level1_code(goalValueOb.getCheckLevel1Code());
      goalValueObj.setCheck_level2_code(goalValueOb.getCheckLevel2Code());
      goalValueObj.setCheck_level3_code(goalValueOb.getCheckLevel3Code());
      goalValueObj.setOut_data_auth_id(goalValueOb.getOutDataAuthId());
      goalValueObj.setData_auth_id(goalValueOb.getDataAuthId());
      goalValueObj.setData_own_department(goalValueOb.getDataOwnDepartment());
      goalValueObjArgs.add(goalValueObj);
    }
    return goalValueObjArgs;
  }

  /**
   * GoalValueObj 转换为 GoalValueObjCommon
   *
   * @return List<GoalValueObjCommon>
   */
  private List<GoalValueObjCommon> copyToCommonList(List<GoalValueObj> paasGoalValues) {
    List<GoalValueObjCommon> list = new ArrayList<>();

    paasGoalValues.forEach(item -> {
      GoalValueObjCommon goalValueObjCommon = new GoalValueObjCommon();
      goalValueObjCommon.setTenantId(item.getTenant_id());
      goalValueObjCommon.setName(item.getName());
      goalValueObjCommon.setCheckObject(item.getCheck_object());
      goalValueObjCommon.setGoalRuleId(item.getGoal_rule_id());
      goalValueObjCommon.setGoalRuleDetailId(item.getGoal_rule_detail_id());
      goalValueObjCommon.setGoalType(item.getGoal_type());
      goalValueObjCommon.setFiscalYear(item.getFiscal_year());
      goalValueObjCommon.setAnnualValue(item.getAnnual_value());
      goalValueObjCommon.setUserId(item.getUser_id());
      goalValueObjCommon.setStringDeptId(item.getStringdept_id());
      goalValueObjCommon.setGoalValueName(item.getGoal_value_name());
      goalValueObjCommon.setOwner(item.getOwner());
      goalValueObjCommon.setLockStatus(item.getLock_status());
      goalValueObjCommon.setLifeStatus(item.getLife_status());
      goalValueObjCommon.setRecordType(item.getRecord_type());
      goalValueObjCommon.setCreatedBy(item.getCreated_by());
      goalValueObjCommon.setCreateTime(item.getCreate_time());
      goalValueObjCommon.setLastModifiedBy(item.getLast_modified_by());
      goalValueObjCommon.setLastModifiedTime(item.getLast_modified_time());
      goalValueObjCommon.setExtendObjDataId(item.getExtend_obj_data_id());
      goalValueObjCommon.setPackageName(item.getPackage_name());
      goalValueObjCommon.setObjectDescribeId(item.getObject_describe_id());
      goalValueObjCommon.setObjectDescribeApiName(item.getObject_describe_api_name());
      goalValueObjCommon.setVersion(item.getVersion());
      goalValueObjCommon.setLockUser(item.getLock_user());
      goalValueObjCommon.setLockRule(item.getLock_rule());
      goalValueObjCommon.setLifeStatusBeforeInvalid(item.getLife_status_before_invalid());
      goalValueObjCommon.setIsDeleted(item.getIs_deleted());
      goalValueObjCommon.setOutTenantId(item.getOut_tenant_id());
      goalValueObjCommon.setOutOwner(item.getOut_owner());
      goalValueObjCommon.setDataAuthCode(item.getData_auth_code());
      goalValueObjCommon.setChangeType(item.getChange_type());
      goalValueObjCommon.setOutDataAuthCode(item.getOut_data_auth_code());
      goalValueObjCommon.setDeptId(item.getDept_id());
      goalValueObjCommon.setId(item.getId());
      goalValueObjCommon.setOid(item.getOid());
      goalValueObjCommon.setCheckObjectId(item.getCheck_object_id());
      goalValueObjCommon.setCheckFieldApiName(item.getCheck_field_api_name());
      goalValueObjCommon.setCheckLevel1Code(item.getCheck_level1_code());
      goalValueObjCommon.setCheckLevel2Code(item.getCheck_level2_code());
      goalValueObjCommon.setCheckLevel3Code(item.getCheck_level3_code());
      goalValueObjCommon.setDataAuthId(item.getData_auth_id());
      goalValueObjCommon.setOutDataAuthId(item.getOut_data_auth_id());
      goalValueObjCommon.setActionDate(item.getAction_date());
      goalValueObjCommon.setDataOwnDepartment(item.getData_own_department());
      goalValueObjCommon.setRuleId(item.getRule_id());
      goalValueObjCommon.setFiscalActionDate(item.getFiscal_action_date());
      goalValueObjCommon.setCycleDate(item.getMonth());
      goalValueObjCommon.setCycleDateValue(item.getMonth_value());
      goalValueObjCommon.setCycleDateTimestamp(item.getFiscal_year_month());
      goalValueObjCommon.setCheckCycle("month");
      list.add(goalValueObjCommon);
    });
    return list;
  }

  public void checkGoalValueObj(SysOffLineArg sysOffLineArg) {
    if (sysOffLineArg.getMandatory()) {
      int pageSize = 10;
      String startId = "0";
      String lastSyncEis = "last_sync_eis";
      List<Map<String, String>> tenantIdIdMapList = new ArrayList<>();
      do {
        try {
          tenantIdIdMapList = objectDataMapper.setTenantId("-1").getLastSyncEisList(startId, pageSize);
          if (CollectionUtils.isEmpty(tenantIdIdMapList)) {
            break;
          }
          Map<String, String> stringStringMap = tenantIdIdMapList.get(tenantIdIdMapList.size() - 1);
          startId = stringStringMap.get("id");
          tenantIdIdMapList.forEach(eiIdMap -> {
            String ei = eiIdMap.get(lastSyncEis);
            this.checkGoalData(ei, Constants.GOAL_VALUE_OBJ);
            this.checkGoalData(ei, "goal_value_obj_week");
            this.checkGoalData(ei, "goal_value_obj_year");
            this.checkGoalData(ei, "goal_value_obj_quarter");
          });
        } catch (Exception e) {
          log.warn("checkGoalValueObj error");
        }
      } while (tenantIdIdMapList.size() >= pageSize);
      log.info("all tenantId check success!");
      return;
    }
    String tenantId = sysOffLineArg.getTenantId();
    List<String> goalRuleIds = sysOffLineArg.getGoalRuleIds();
    String tableName = Optional.ofNullable(sysOffLineArg.getTableName()).orElse(Constants.GOAL_VALUE_OBJ);
    List<String> tenantIdList = Splitter.on(",|").trimResults().omitEmptyStrings().splitToList(tenantId);
    tenantIdList.forEach(ei -> this.checkGoalData(ei, goalRuleIds, tableName));
  }

  private void checkGoalData(String ei, List<String> goalRuleIds, String tableName) {
    List<String> goalValueIdList;
    if (CollectionUtils.isEmpty(goalRuleIds)) {
      goalValueIdList = goalMapper.setTenantId(ei).getInValidGoalValueIdList(ei, tableName);
    } else {
      goalValueIdList = goalMapper.setTenantId(ei)
                                  .getInvalidGoalValueIdListByRuleId(ei, tableName, SqlUtils.generateInExpress(goalRuleIds));
    }
    if (CollectionUtils.isNotEmpty(goalValueIdList)) {
      // 逻辑删除 goal_value_obj*
      int num = objectDataMapper.setTenantId(ei)
                                .logicDelGoalObjByOid(ei, tableName, SqlUtils.generateInExpress(goalValueIdList));
      log.info("checkGoalValueObj goalValue has deleted, goal_value_obj not deleted, tenantId:{}, goalValueIdList:{}, num:{}", ei, JSON.toJSONString(goalValueIdList), num);
    }
  }

  private void checkGoalData(String ei, String tableName) {
    List<String> goalValueIdList = goalMapper.setTenantId(ei).getInValidGoalValueIdList(ei, tableName);
    if (CollectionUtils.isNotEmpty(goalValueIdList)) {
      // 逻辑删除 goal_value_obj*
      int num = objectDataMapper.setTenantId(ei)
                                .logicDelGoalObjByOid(ei, tableName, SqlUtils.generateInExpress(goalValueIdList));
      log.info("checkGoalValueObj goalValue has deleted, goal_value_obj not deleted, tenantId:{}, goalValueIdList:{}, num:{}", ei, JSON.toJSONString(goalValueIdList), num);
    }
  }
}

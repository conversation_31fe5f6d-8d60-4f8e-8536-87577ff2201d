package com.fxiaoke.bi.paas2bi.transfer.areasync.mapper;

import com.fxiaoke.bi.paas2bi.transfer.areasync.bean.DimSysArea;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ICrudPaginationMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * dim_sys_area
 *
 * @Entity {@link com.fxiaoke.bi.paas2bi.transfer.areasync.bean.DimSysArea}
 */
@Repository
public interface DimSysAreaMapper extends ICrudPaginationMapper<DimSysArea>, IBatchMapper<DimSysArea>, ITenant<DimSysAreaMapper> {

  @Update("UPDATE dim_sys_area_gray SET is_deleted = 1 WHERE ei = #{ei} AND country_area_id = ANY(ARRAY[#{countryAreaIds}])")
  int deleteByIdArr(@Param("ei") Integer tenantId, @Param("countryAreaIds") String[] paasAreaIds);

  @Select("SELECT count(*) FROM dim_sys_area_gray WHERE ei = -1")
  Long getSysDimAreaRows();

  @Select("SELECT count(*) FROM dim_sys_area_gray WHERE ei = #{ei}")
  Long getTenantDimAreaRows(@Param("ei") Integer ei);
}
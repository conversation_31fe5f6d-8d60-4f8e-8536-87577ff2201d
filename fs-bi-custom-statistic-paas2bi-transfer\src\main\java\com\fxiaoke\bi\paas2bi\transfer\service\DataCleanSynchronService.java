package com.fxiaoke.bi.paas2bi.transfer.service;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.paas2bi.transfer.bean.OpLog;
import com.fxiaoke.bi.paas2bi.transfer.bean.PCNode;
import com.fxiaoke.bi.paas2bi.transfer.bean.ProductCategory;
import com.fxiaoke.bi.paas2bi.transfer.bean.ProductCategoryEvent;
import com.fxiaoke.bi.paas2bi.transfer.mapper.bi.DataCleanSychonMapper;
import com.fxiaoke.bi.paas2bi.transfer.utils.CommonsUtils;
import com.fxiaoke.bi.paas2bi.transfer.utils.GrayManagerUtil;
import com.fxiaoke.bi.paas2bi.transfer.utils.ProductCategoryStatementExecutor;
import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.common.Pair;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.helper.StringHelper;
import com.fxiaoke.jdbc.JdbcConnection;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;
import com.github.trace.TraceContext;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2021/03/30
 * @Description 同步产品的层级字段表
 */
@Slf4j
@Service
public class DataCleanSynchronService implements MessageListenerConcurrently {

  private final DataCleanSychonMapper dataCleanSychonMapper;
  private final BIPGDataSource bipgDataSource;
  private final EIEAConverter eieaConverter;
  private final NomonProducer nomonProducer;

  private static final String sql = " update %s.product_category set parent_category_id=?,category_path=? where tenant_id=? and id=? ";

  public DataCleanSynchronService(DataCleanSychonMapper dataCleanSychonMapper,
                                  BIPGDataSource bipgDataSource,
                                  @Autowired(required = false) EIEAConverter eieaConverter,
                                  NomonProducer nomonProducer) {
    this.dataCleanSychonMapper = dataCleanSychonMapper;
    this.bipgDataSource = bipgDataSource;
    this.eieaConverter = eieaConverter;
    this.nomonProducer = nomonProducer;
  }

  /**
   * 发送日晷延时处理
   */
  public void send2Gnomon(List<OpLog> opLogs) {
    if (CollectionUtils.isNotEmpty(opLogs)) {
      Set<String> tenantIds = opLogs.stream().filter(opLog -> {
        String kind = opLog.getKind();
        if ("insert".equals(kind)) {
          return true;
        } else if ("update".equals(kind)) {
          Set<String> changeFieldList = CommonsUtils.getChangeInfoListNew(opLog);
          return changeFieldList.contains("pid");
        }
        return false;
      }).map(OpLog::getTenantId).filter(Objects::nonNull).collect(Collectors.toSet());
      tenantIds.forEach(tenantId -> {
        NomonMessage message = NomonMessage.builder()
                                           .biz("bi-refresh-productCategory-biz")
                                           .tenantId(tenantId)
                                           .dataId("product_category")
                                           .taskId("product_category")
                                           .executeTime(new Date(System.currentTimeMillis() + 10 * 60 * 1000))
                                           .callArg(new ProductCategoryEvent(tenantId, "product_category", 0).toJSONString())
                                           .build();
        nomonProducer.send(message);
        log.info("send bi-refresh-productCategory-biz nomon message tenantId:{},message:{}", tenantId, message);
      });
    }
  }


  @Override
  public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
    for (MessageExt msg : msgs) {
      ProductCategoryEvent productCategoryEvent = ProductCategoryEvent.parseFromJSON(StringHelper.toString(msg.getBody()));
      String tenantId = productCategoryEvent.getTenantId();
      CommonsUtils.createTrace(eieaConverter, tenantId, ObjectId.get().toString());
      StopWatch stopWatch = new StopWatch();
      try {
        stopWatch.start("begin etl");
        this.dataCleanSync(tenantId);
        stopWatch.stop();
        log.info("success product category tenantId:{},cost:{} ms", tenantId, stopWatch.getTotalTimeMillis());
      } catch (Exception e) {
        log.error("product category etl error tenantId:{}", tenantId, e);
        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
      } finally {
        if (TraceContext.exist()) {
          TraceContext.remove();
        }
      }
    }
    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
  }

  public void dataCleanSynchronMain(OpLog opLog) {
    StopWatch dataClean = StopWatch.createUnStarted("begin dataClean");
    dataClean.start();
    String tenantId = opLog.getTenantId();
    try {
      String kind = opLog.getKind();
      String id = opLog.getKeys().get("id").toString();
      if ("insert".equals(kind)) {
        this.insertProductCategoryById(tenantId, id);
      } else if ("update".equals(kind)) {
        Set<String> changeFieldList = CommonsUtils.getChangeInfoListNew(opLog);
        if (changeFieldList.contains("pid")) {
          this.updateProductCategoryByIdPlus(tenantId, id);
        }
      }
      dataClean.stop();
      log.info("dataCleanSynchronMain success tenantId:{} topic:{},messageId:{} cost:{} ms", tenantId, opLog.getTopic(), opLog.getMsgId(), dataClean.getTotalTimeMillis());
    } catch (Exception e) {
      log.error("dataCleanSynchronMain error tenantId:{} topic:{},messageId:{}", tenantId, opLog.getTopic(), opLog.getMsgId(), e);
      throw e;
    }
  }

  //根据企业id获取所有产品分类列表
  public void dataCleanSync(String tenantId) {
    if (GrayManagerUtil.skipEtlProductCategory(tenantId)) {
      return;
    }
    long start = System.currentTimeMillis();
    RouterInfo routerInfo = bipgDataSource.getRouterInfo(tenantId);
    if (null == routerInfo) {
      log.error("can not find router info tenantId:{}", tenantId);
      return;
    }
    String schemaName = "public";
    if (bipgDataSource.isSchema(tenantId)) {
      schemaName = "sch_" + tenantId;
    }
    List<ProductCategory> productCategories = dataCleanSychonMapper.setTenantId(tenantId)
                                                                   .findProductCategoryByTenantId(tenantId);
    if (CollectionUtils.isNotEmpty(productCategories)) {
      int updateSize = 0;
      Map<String, PCNode> pcNodeMap = this.etlCategoryPath(productCategories);
      try (JdbcConnection connection = bipgDataSource.getJdbcConnectionByTenantID(tenantId)) {
        String updateSQL = String.format(sql, schemaName);
        ProductCategoryStatementExecutor productCategoryStatementExecutor = new ProductCategoryStatementExecutor("etl_product_category", updateSQL, connection.connection(), 100);
        for (Map.Entry<String, PCNode> kv : pcNodeMap.entrySet()) {
          PCNode node = kv.getValue();
          String codePath = node.getCategoryPath();
          String cateGoryPathValue = null;
          if (StringUtils.isNotBlank(codePath)) {
            cateGoryPathValue = node.getCategoryPath();
          }
          ProductCategory productCategory = new ProductCategory();
          productCategory.setTenantId(tenantId);
          productCategory.setId(node.getId());
          productCategory.setCategoryPath(cateGoryPathValue);
          productCategory.setParentCategoryId(node.getParentCategoryId());
          productCategoryStatementExecutor.accept(productCategory);
          updateSize++;
        }
        productCategoryStatementExecutor.flush();
        log.info("dataCleanSync success tenantId:{},size:{},cost:{}ms", tenantId, updateSize,
          System.currentTimeMillis() - start);
      } catch (Exception e) {
        log.error("dataCleanSync error tenantId:{}", tenantId, e);
        throw new RuntimeException(e);
      }
    }
    //增加埋点
    Map<String, Object> deptPathLog = Maps.newHashMap();
    deptPathLog.put("tenantId", tenantId);
    deptPathLog.put("type", "category_path");
    try {
      DataPersistor.addEvent("flush_ltree_field", deptPathLog);
    } catch (Exception e) {
      log.error("data persist error logs:{}", JSON.toJSONString(deptPathLog), e);
    }
  }

  /**
   * @param productCategories 产品分类集合
   * @return 清洗 category path 生成链表
   */
  public Map<String, PCNode> etlCategoryPath(List<ProductCategory> productCategories) {
    Map<String, PCNode> resultMap = productCategories.stream()
                                                     .collect(Collectors.toMap(ProductCategory::getId, productCategory -> {
                                                       PCNode pcNode = new PCNode();
                                                       pcNode.setId(productCategory.getId());
                                                       pcNode.setPid(productCategory.getPid());
                                                       pcNode.setCode(productCategory.getCode());
                                                       pcNode.setName(productCategory.getName());
                                                       return pcNode;
                                                     }));
    for (Map.Entry<String, PCNode> kv : resultMap.entrySet()) {
      PCNode pn = kv.getValue();
      String pid = pn.getPid();
      if (StringUtils.isNotBlank(pid)) {
        PCNode parentNode = resultMap.get(pid);
        if (parentNode != null) {
          pn.setParentNode(parentNode);
        }
      }
    }
    return resultMap;
  }

  /**
   * 考虑三种情况：todo 考虑并发情况 加分布式锁
   * 1、子节点移动到别的子节点
   * 2、子节点易动到根节点
   * 3、根节点移动到子节点
   */
  public void updateProductCategoryByIdPlus(String tenantId, String id) {
    CommonsUtils.createTrace(eieaConverter, tenantId, ObjectId.get().toString());
    ProductCategory productCategory = dataCleanSychonMapper.setTenantId(tenantId).findProductCategoryById(tenantId, id);
    //验证是否为空可能已经被物理删除了等情况
    if (productCategory != null) {
      //当前的pid
      String newPid = productCategory.getPid();
      String oldCategoryPath = productCategory.getCategoryPath();
      String code = productCategory.getCode();
      String subCategoryPath;
      if (StringUtils.isNotBlank(oldCategoryPath)) {
        //找到所有的子节点
        subCategoryPath = oldCategoryPath + "." + code;
      } else {
        //oldCategoryPath 为空说明之前是一个根节点
        subCategoryPath = String.valueOf(code);
      }
      //获取当前节点的parent_category_id 和 category_path
      Pair<String, String> codeAndPath = this.getParentCategoryIdAndPath(tenantId, newPid);
      String categoryPath = StringUtils.isBlank(codeAndPath.second) ? "null" : "'" + codeAndPath.second + "'";
      log.info("parent node tenantId:{},id:{},name:{},pid:{},code:{},parentCategoryId:{},categoryPath:{}", tenantId, id, productCategory.getName(), newPid, code, codeAndPath.first, categoryPath);
      dataCleanSychonMapper.setTenantId(tenantId)
                           .updateProductCategoryById(codeAndPath.first, categoryPath, tenantId, id);
      //获取子节点并拼接parent_category_id 和 category_path
      List<ProductCategory> productCategories = dataCleanSychonMapper.setTenantId(tenantId)
                                                                     .findAllSubPCs(tenantId, subCategoryPath);
      if (CollectionUtils.isNotEmpty(productCategories)) {
        Map<String, PCNode> linkData = this.etlCategoryPath(productCategories);
        String parentPath = String.valueOf(code);
        if (StringUtils.isNotBlank(codeAndPath.second)) {
          parentPath = codeAndPath.second + "." + code;
        }
        //给每个子节点拼接parent_category_id 和 category_path
        for (Map.Entry<String, PCNode> kv : linkData.entrySet()) {
          PCNode node = kv.getValue();
          String parentCategoryId = node.getParentCategoryId();
          if (StringUtils.isBlank(parentCategoryId)) {
            parentCategoryId = String.valueOf(code);
          }
          String codePath = node.getCategoryPath();
          String cateGoryPathValue;
          if (StringUtils.isNotBlank(codePath)) {
            cateGoryPathValue = "'" + parentPath + "." + codePath + "'";
          } else {
            cateGoryPathValue = "'" + parentPath + "'";
          }
          log.info("parent node tenantId:{},id:{},name:{},pid:{},code:{},parentCategoryId:{},categoryPath:{}", tenantId, node.getId(), productCategory.getName(), node.getPid(), node.getCode(), parentCategoryId, cateGoryPathValue);
          dataCleanSychonMapper.setTenantId(tenantId)
                               .updateProductCategoryById(parentCategoryId, cateGoryPathValue, tenantId, node.getId());
        }
      }
    }
  }

  /**
   * 获取父节点的code 和 category_path
   * 考虑没有父节点的情况
   *
   * @param tenantId tenantId
   * @param pid      parentId
   * @return Pair<String, String>
   */
  public Pair<String, String> getParentCategoryIdAndPath(String tenantId, String pid) {
    if (StringUtils.isBlank(pid)) {
      return new Pair<>(null, null);
    }
    ProductCategory productCategory = dataCleanSychonMapper.setTenantId(tenantId)
                                                           .findProductCategoryById(tenantId, pid);
    if (productCategory != null) {
      //更新当前code的category_path字段
      String parentCode = productCategory.getCode();
      String parentCategoryPath = productCategory.getCategoryPath();
      String categoryPath;
      if (StringUtils.isBlank(parentCategoryPath)) {
        categoryPath = parentCode;
      } else {
        categoryPath = parentCategoryPath + "." + parentCode;
      }
      return new Pair<>(parentCode, categoryPath);
    }
    return new Pair<>(null, null);
  }

  /**
   * 插入产品层级字段
   *
   * @param tenantId 企业id
   * @param id       当前id
   */
  public void insertProductCategoryById(String tenantId, String id) {
    CommonsUtils.createTrace(eieaConverter, tenantId, ObjectId.get().toString());
    //查询上级产品的层级
    //通过id查询对应的数据
    Map<String, String> productCategoryMap = dataCleanSychonMapper.setTenantId(tenantId)
                                                                  .findProductByParentId(tenantId, id);
    if (MapUtils.isEmpty(productCategoryMap)) {
      log.warn("can not find product_category by id tenantId:{},id:{}", tenantId, id);
      return;
    }
    Object pid = productCategoryMap.get("pid");
    if (pid != null) {
      String parentId = pid.toString();
      //更新当前的parent_category_id和category_path字段
      //根据pid获取上级的category_path字段
      Map<String, String> superMap = dataCleanSychonMapper.setTenantId(tenantId)
                                                          .findProductByParentId(tenantId, parentId);
      if (MapUtils.isEmpty(superMap)) {
        log.error("can not find product by parent id is tenantId:{},parentId:{}", tenantId, parentId);
        return;
      }
      //更新当前id的category_path字段
      String superCode = superMap.get("code");
      Object parentIdValue = superMap.get("category_path");
      String superCodeStr = StringUtils.isBlank(superCode) ? "null" : "'" + superCode + "'";
      String parentIdValueStr = "null";
      if (parentIdValue == null) {
        if (StringUtils.isNotBlank(superCode)) {
          parentIdValueStr = "'" + superCode + "'";
        }
      } else {
        if (StringUtils.isNotBlank(superCode)) {
          parentIdValueStr = "'" + parentIdValue + "." + superCode + "'";
        }
      }
      String updateSql = String.format("update product_category set parent_category_id=%s , category_path=%s where tenant_id='%s' and id='%s'", superCodeStr, parentIdValueStr, tenantId, id);
      dataCleanSychonMapper.setTenantId(tenantId).updateProductById(updateSql);
    }
  }
}

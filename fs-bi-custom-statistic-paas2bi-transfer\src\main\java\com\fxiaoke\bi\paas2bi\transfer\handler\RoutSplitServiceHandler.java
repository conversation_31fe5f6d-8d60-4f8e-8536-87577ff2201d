package com.fxiaoke.bi.paas2bi.transfer.handler;

import com.fxiaoke.bi.paas2bi.transfer.service.BIPGDataSource;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2019/7/12.
 */
@Slf4j
@Service
public class RoutSplitServiceHandler {

  private final BIPGDataSource bipgDataSource;

  public RoutSplitServiceHandler(BIPGDataSource bipgDataSource) {
    this.bipgDataSource = bipgDataSource;
  }

  /**
   * 分拆企业id，按照路由的实例分组
   *
   * @param tenantList tenant_id list
   */
  public Map<String, List<String>> splitTenants(List<String> tenantList) {
    Map<String, List<String>> routMaps = Maps.newHashMap();
    if (tenantList != null) {
      tenantList.forEach(tenantId -> {
        String masterIP = bipgDataSource.getPGMasterServer(tenantId);
        String ip = masterIP.substring(0, masterIP.lastIndexOf('/'));
        routMaps.computeIfAbsent(ip, key -> Lists.newArrayList()).add(tenantId);
      });
    }
    return routMaps;
  }

  public static class Pair<K, V> {
    @Setter
    @Getter
    private K k;
    @Setter
    @Getter
    private V v;

    public Pair(K k, V v) {
      this.k = k;
      this.v = v;
    }
  }
}

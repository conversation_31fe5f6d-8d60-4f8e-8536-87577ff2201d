package com.fxiaoke.bi.paas2bi.transfer.dao.impl;

import com.fxiaoke.bi.paas2bi.transfer.dao.MultiLangTableDao;
import com.fxiaoke.bi.paas2bi.transfer.entity.MultiLangTableEntity;
import com.fxiaoke.bi.paas2bi.transfer.mapper.bi.MultiRecordTableMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 多语言表DAO实现
 */
@Slf4j
@Repository
public class MultiLangTableDaoImpl implements MultiLangTableDao {

  private final MultiRecordTableMapper multiRecordTableMapper;

  public MultiLangTableDaoImpl(MultiRecordTableMapper multiRecordTableMapper) {
    this.multiRecordTableMapper = multiRecordTableMapper;
  }

  @Override
  public int batchInsert(List<MultiLangTableEntity> entities) {
    if (CollectionUtils.isEmpty(entities)) {
      return 0;
    }

    try {
      // 获取第一个实体的租户ID作为操作的租户ID
      String tenantId = entities.get(0).getTenantId();
      return multiRecordTableMapper.setTenantId(tenantId).batchInsert(entities);
    } catch (Exception e) {
      log.error("批量插入多语言表记录失败", e);
      return 0;
    }
  }
} 
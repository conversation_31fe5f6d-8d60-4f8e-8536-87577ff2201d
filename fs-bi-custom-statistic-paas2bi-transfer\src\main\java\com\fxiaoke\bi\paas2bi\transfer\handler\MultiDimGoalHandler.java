package com.fxiaoke.bi.paas2bi.transfer.handler;

import com.fxiaoke.bi.paas2bi.transfer.bean.OpLog;
import com.fxiaoke.bi.paas2bi.transfer.bean.PageInfo;
import com.fxiaoke.bi.paas2bi.transfer.dao.GoalValueDao;
import com.fxiaoke.bi.paas2bi.transfer.mapper.paas.GoalMapper;
import com.fxiaoke.bi.paas2bi.transfer.pojo.CheckDimensionField;
import com.fxiaoke.bi.paas2bi.transfer.pojo.GoalValuePlus;
import com.fxiaoke.bi.paas2bi.transfer.service.BIPGDataSource;
import com.fxiaoke.bi.paas2bi.transfer.service.GoalRuleCacheService;
import com.fxiaoke.bi.statistic.common.utils.GrayManager;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 处理多维目标
 */
@Slf4j
@Service
public class MultiDimGoalHandler extends BatchHandler {

  final GoalValueDao goalValueDao;
  final GoalMapper goalMapper;
  final GoalRuleCacheService goalRuleCacheService;
  final BIPGDataSource bipgDataSource;

  public MultiDimGoalHandler(GoalValueDao goalValueDao,
                             GoalMapper goalMapper,
                             GoalRuleCacheService goalRuleCacheService,
                             BIPGDataSource bipgDataSource) {
    this.goalValueDao = goalValueDao;
    this.goalMapper = goalMapper;
    this.goalRuleCacheService = goalRuleCacheService;
    this.bipgDataSource = bipgDataSource;
  }

  /**
   * 只计算 data_auth_code 变更的
   */
  @Override
  public void batchHandler(List<OpLog> opLogs) {
    opLogs = opLogs.stream()
                   .filter(opLog -> GrayManager.isAllowByRule("MultiDimGoalEtl", opLog.getTenantId()))
                   .collect(Collectors.toList());
    // 根据企业id将受影响的对象名分组
    Map<String, Set<String>> tenantApiMap = new HashMap<>();
    for (OpLog opLog : opLogs) {
      if (!opLog.getTable().startsWith("goal_value_obj") && !opLog.isTouch() && "update".equals(opLog.getKind()) &&
          StringUtils.isNotBlank(opLog.findPrimKey()) && (opLog.findChangeInfoListNew().contains("data_auth_code"))) {
        // 将每个企业下的对象分组
        tenantApiMap.merge(opLog.getTenantId(), Sets.newHashSet(opLog.getObject_describe_api_name()), (a, b) -> {
          b.addAll(a);
          return b;
        });
      }
    }

    tenantApiMap.forEach((tenantId, apiNameSet) -> {
      List<CheckDimensionField> effectedGoalRuleList = goalRuleCacheService.getEffectedGoalRuleList(tenantId, apiNameSet);
      if (CollectionUtils.isEmpty(effectedGoalRuleList)) {
        return;
      }
      // key: ruleId, value: 受影响的 data_auth_code
      Map<String, List<CheckDimensionField>> ruleIdCheckDimsMap = effectedGoalRuleList.stream()
                                                                                      .collect(Collectors.groupingBy(CheckDimensionField::getId));
      ruleIdCheckDimsMap.forEach((ruleId, checkDimensionFieldList) -> {
        PageInfo<GoalValuePlus> pageInfo = new PageInfo<>(null, "0", 10);
        int ruleObjCount = 0;
        do {
          String lastFlag = pageInfo.getLastFlag();
          List<GoalValuePlus> goalValuePluses = goalMapper.setTenantId(tenantId)
                                                          .getGoalValueObjByRuleId(tenantId, ruleId, lastFlag, pageInfo.getPageSize());
          // 如果规则没有目标值，就继续下一个目标规则
          if (CollectionUtils.isEmpty(goalValuePluses)) {
            return;
          }
          ruleObjCount = ruleObjCount + goalValuePluses.size();
          pageInfo.setQueryList(goalValuePluses);
          GoalValuePlus lastGoalValuePlus = goalValuePluses.get(goalValuePluses.size() - 1);
          pageInfo.setLastFlag(lastGoalValuePlus.getId());
          goalValueDao.updateMultiDimGoalFields(tenantId, pageInfo, bipgDataSource.isSchema(tenantId), checkDimensionFieldList);
          log.info("MultiDimGoalHandler success etl batch tenant:{},size:{}", tenantId, pageInfo.queryListSize());
        } while (!pageInfo.isLastPage());
        log.info("goal_rule multi etl success etl batch tenant:{}, goal_rule_id:{}, count:{}.", tenantId, ruleId, ruleObjCount);
      });
    });
  }
}

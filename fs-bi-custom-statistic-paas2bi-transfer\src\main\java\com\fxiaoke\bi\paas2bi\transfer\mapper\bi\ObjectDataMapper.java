package com.fxiaoke.bi.paas2bi.transfer.mapper.bi;

import com.fxiaoke.bi.paas2bi.transfer.bean.BiMtDimensionDO;
import com.fxiaoke.bi.paas2bi.transfer.pojo.CheckDimensionField;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface ObjectDataMapper extends ITenant<ObjectDataMapper> {

  @Select("SELECT data_auth_code FROM ${tableName} WHERE tenant_id = #{tenantId} AND ${idColumn} = #{objectId}")
  String getDataAuthCodeById(@Param("tableName") String tableName,
                             @Param("tenantId") String tenantId,
                             @Param("idColumn") String idColumn,
                             @Param("objectId") String objectId);

  @Select("${sql}")
  List<CheckDimensionField> getDataAuthCodeListById(@Param("sql") String sql);

  @Select("SELECT * FROM bi_mt_dimension WHERE tenant_id = #{tenantId} AND topology_describe_id = #{topologyDescribeId}")
  List<BiMtDimensionDO> getDimensionIdByRuleId(@Param("tenantId") String tenantId,
                                               @Param("topologyDescribeId") String topologyDescribeId);

  @Select("SELECT last_sync_eis, id FROM db_sync_info WHERE is_deleted = 0 AND id > #{startId} ORDER BY id LIMIT 10")
  List<Map<String, String>> getLastSyncEisList(@Param("startId") String startId, @Param("limit") Integer limit);

  @Update("UPDATE ${tableName} SET is_deleted = -1, last_modified_time = EXTRACT(EPOCH FROM now())::bigint WHERE tenant_id = #{tenantId} AND oid ${oids}")
  int logicDelGoalObjByOid(@Param("tenantId") String tenantId,
                           @Param("tableName") String tableName,
                           @Param("oids") String oids);
}

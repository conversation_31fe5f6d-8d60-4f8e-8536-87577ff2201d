package com.fxiaoke.bi.paas2bi.transfer.mapper;

import com.fxiaoke.bi.paas2bi.transfer.mapper.bi.GoalValueHandlerMapper;
import com.fxiaoke.bi.paas2bi.transfer.mapper.bi.OrgDeptMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2020/11/17
 * @Description
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext.xml")
public class GoalValueHandlerMapperTest {
  @Autowired(required = false)
  GoalValueHandlerMapper goalValueHandlerMapper;
  @Autowired
  OrgDeptMapper orgDeptMapper;
  @BeforeClass
  public static void initClass() {

    System.setProperty("process.profile", "fstest");
//     System.setProperty("process.profile","firstshare");
  }
  @Test
  public void getMap() {
    Map<String, Object> map=goalValueHandlerMapper.setTenantId("74745").getMap("74745");
    System.out.println(map);
  }

  @Test
  public void testDeptPath(){
  System.out.println(orgDeptMapper.setTenantId("78060").updateDeptPath("78060"));
  }
}
package com.fxiaoke.bi.paas2bi.transfer.mapper.bi;

import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SchRunMapper extends ITenant<SchRunMapper> {
  @Update("<script>" + "UPDATE sch_run " + "SET is_active = 0 " + "WHERE sch_run.is_active = 1 " +
    "AND sch_run.is_delete = 0 " + "AND sch_run.ei = #{ei} "  + "AND sch_run.sch_type != 99 " + "AND sch_run.view_id IN " +
    "<foreach collection='viewIds' item='viewId' open='(' separator=',' close=')'>" + "#{viewId}" + "</foreach>" +
    "</script>")
  void clearSchRunByViewDelete(@Param("viewIds") List<String> viewIds,@Param("ei") int ei);
}

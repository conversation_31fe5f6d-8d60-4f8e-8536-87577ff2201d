package com.fxiaoke.bi.paas2bi.transfer.consumer;

import com.fxiaoke.bi.paas2bi.transfer.service.MultiLangRecordListener;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * 消费多语言表消息
 * 专门处理以 "_lang" 结尾的消息，从 ConsumerMQ 中迁移而来
 */
@Slf4j
@Component
public class MultiLangRecordConsumer implements ApplicationListener<ContextRefreshedEvent> {

  private AutoConfMQPushConsumer consumer;
  private final MultiLangRecordListener multiLangRecordListener;

  public MultiLangRecordConsumer(MultiLangRecordListener multiLangRecordListener) {
    this.multiLangRecordListener = multiLangRecordListener;
  }

  @PostConstruct
  public void init() {
    consumer = new AutoConfMQPushConsumer("fs-bi-dim.ini", "multiLangRecordConsumer", multiLangRecordListener);
    log.info("MultiLangRecordConsumer 初始化完成");
  }

  @PreDestroy
  public void destroy() {
    if (consumer != null) {
      consumer.close();
      log.info("MultiLangRecordConsumer 已关闭");
    }
  }

  @Override
  public void onApplicationEvent(ContextRefreshedEvent event) {
    if (null == event.getApplicationContext().getParent()) {
      if (null != consumer) {
        consumer.start();
        log.info("MultiLangRecordConsumer 已启动");
      }
    }
  }

  public void start() {
    if (null != consumer) {
      consumer.start();
      log.info("MultiLangRecordConsumer start success");
    }
  }
}

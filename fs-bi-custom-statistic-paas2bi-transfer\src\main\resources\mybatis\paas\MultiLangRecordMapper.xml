<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fxiaoke.bi.paas2bi.transfer.mapper.paas.MultiLangRecordMapper">


  <!-- 查询带有多语言字段的表名 -->
  <select id="findTablesWithMultiLangFields" resultType="java.lang.String">
    SELECT DISTINCT table_name
    FROM information_schema.tables
    WHERE table_schema = #{schemaName}
      AND table_name LIKE '%_lang'
  </select>

  <!-- 查询表中所有记录的ID -->
  <select id="findAllRecordIds" resultType="java.lang.Integer">
    SELECT count(1)
    FROM ${schemaName}.${tableName}
    WHERE tenant_id = #{tenantId}
      AND lang IS NOT NULL
  </select>

  <select id="findRecordByTenantId" resultType="com.fxiaoke.bi.paas2bi.transfer.bean.ObjectLangRecord">
    SELECT DISTINCT tenant_id,
    <choose>
      <when test="tableName == 'object_data_lang'">
        object_describe_api_name
      </when>
      <otherwise>
        describe_api_name
      </otherwise>
    </choose>
    AS describe_api_name, lang
    FROM ${schemaName}.${tableName}
    WHERE tenant_id = #{tenantId}
  </select>

  <select id="findRecordByApiName" resultType="com.fxiaoke.bi.paas2bi.transfer.bean.ObjectLangRecord">
    SELECT DISTINCT tenant_id,
    <choose>
      <when test="tableName == 'object_data_lang'">
        object_describe_api_name
      </when>
      <otherwise>
        describe_api_name
      </otherwise>
    </choose>
    AS describe_api_name, lang
    FROM ${schemaName}.${tableName}
    WHERE tenant_id = #{tenantId}
    AND
    <choose>
      <when test="tableName == 'object_data_lang'">
        object_describe_api_name = #{apiName}
      </when>
      <otherwise>
        describe_api_name = #{apiName}
      </otherwise>
    </choose>
  </select>

  <select id="findRecordById" resultType="java.util.Map">
    SELECT *
    FROM ${schemaName}.${tableName}
    WHERE tenant_id = #{tenantId}
      AND id = #{primaryKey}
  </select>

  <!-- 查询表中的多语言字段 -->
  <select id="findMultiLangFields" resultType="java.lang.String">
    SELECT DISTINCT column_name
    FROM information_schema.columns
    WHERE table_schema = #{schemaName}
      AND table_name = #{tableName}
      AND column_name LIKE '%_l'
  </select>

  <!-- 批量根据ID查询记录 -->
  <select id="findRecordsByIds" resultType="java.util.Map">
    SELECT * FROM ${schemaName}.${tableName}
    WHERE tenant_id = #{tenantId}
    AND id IN
    <foreach collection="primaryKeys" item="primaryKey" open="(" close=")" separator=",">
      #{primaryKey}
    </foreach>
  </select>
</mapper>
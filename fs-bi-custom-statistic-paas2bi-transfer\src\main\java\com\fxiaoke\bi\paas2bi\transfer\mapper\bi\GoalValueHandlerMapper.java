package com.fxiaoke.bi.paas2bi.transfer.mapper.bi;

import com.fxiaoke.bi.paas2bi.transfer.mapper.provider.GoalValueObjProvider;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ICrudPaginationMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2020/11/17
 * @Description
 */
@Repository
public interface GoalValueHandlerMapper extends ICrudPaginationMapper, IBatchMapper, ITenant<GoalValueHandlerMapper> {

  @Select("Select * from agg_rule where tenant_id=#{tenantId} limit 1")
  Map<String, Object> getMap(@Param("tenantId") String tenantId);

  /**
   * 获取当前字段落的槽位值
   *
   * @param tenantId
   * @param objectName
   * @param objectNameUdef
   * @param field_name
   * @return
   */
  @Select("select field_location,type from udf_obj_field where ei=#{tenantId} and (db_obj_name = #{objectName} or  db_obj_name =  #{objectNameUdef}) and db_field_name = #{field_name}")
  Map<String, String> getFiledLocation(@Param("tenantId") Integer tenantId,
                                       @Param("objectName") String objectName,
                                       @Param("objectNameUdef") String objectNameUdef,
                                       @Param("field_name") String field_name);

  @Select("Select " + "data_auth_id," + "out_data_auth_id," + "data_auth_code," + "out_data_auth_code," +
          "data_own_department," + "owner," + "out_owner," + "out_tenant_id," + "${filedLocation} check_level_code_1," +
          "${idFieldLocation} check_level_code_2  " +
          "from ${tablenName} where tenant_id=#{tenantId} and ${idFieldLocation}=#{objectId}")
  Map<String, Object> getFieldValue(@Param("tenantId") String tenantId,
                                    @Param("filedLocation") String filedLocation,
                                    @Param("tablenName") String tablenName,
                                    @Param("idFieldLocation") String idFieldLocation,
                                    @Param("objectId") String objectId);

  @SelectProvider(type = GoalValueObjProvider.class, method = "getFieldValue")
  Map<String, Object> getFieldValuePlus(@Param("tenantId") String tenantId,
                                        @Param("filedLocation") String filedLocation,
                                        @Param("objectApiName") String objectApiName,
                                        @Param("objectId") String objectId,
                                        @Param("schema") boolean isSchema);

  @Select("select " + "data_auth_id," + "out_data_auth_id," + "data_auth_code," + "out_data_auth_code," +
          "data_own_department," + "main_department check_level_code_1 ," + "user_id check_level_code_2 " +
          "from org_employee_user  " + "where  tenant_id=#{tenantId} and user_id = #{userId} limit 1 ")
  Map<String, Object> getUserId(@Param("tenantId") String tenantId, @Param("userId") String userId);


  @Select("select " + "t0.data_auth_code," + "t0.out_data_auth_code," + "t1.main_department check_level_code_1 ," +
          "t1.user_id check_level_code_2 ," + "t0.id check_level_code_3, " + "t0.data_auth_id," +
          "t0.out_data_auth_id," + "t0.out_tenant_id," + "t0.out_owner," + "t0.owner," + "t0.data_own_department " +
          "from ${tableName} t0 LEFT JOIN  org_employee_user t1 on ( t0.tenant_id=t1.tenant_id and t0.owner=t1.user_id) " +
          " where t0.tenant_id= #{tenantId} and t0.id=#{objectId}")
  Map<String, Object> getObjectDetail(@Param("tenantId") String tenantId,
                                      @Param("objectId") String objectId,
                                      @Param("tableName") String tableName);

  @SelectProvider(type = GoalValueObjProvider.class, method = "queryObjectDataDetail")
  Map<String, Object> getObjectDetailPlus(@Param("tenantId") String tenantId,
                                          @Param("objectId") String objectId,
                                          @Param("objectDescribeApiName") String objectDescribeApiName,
                                          @Param("schema") boolean schema);

  @Select(
    "select " + "t1.main_department check_level_code_1 ," + "t1.user_id check_level_code_2," + "t0.data_auth_id," +
    "t0.out_data_auth_id," + "data_auth_code," + "out_data_auth_code," + "data_own_department " + "from  " +
    "object_data t0 LEFT JOIN  org_employee_user t1 on ( t0.tenant_id=t1.tenant_id and t0.owner=t1.user_id) " +
    " where t0.tenant_id= #{tenantId} and t0.value0=#{objectId} and t0.object_describe_api_name=#{apiName}")
  Map<String, Object> getObjectDetailUdef(@Param("tenantId") String tenantId,
                                          @Param("objectId") String objectId,
                                          @Param("apiName") String apiName);

  @Update("${sql}")
  void updateGoalValueObj(@Param("sql") String sql);

  @Select(
    "select " + " DISTINCT " + " gv.tenant_id,\n" + " gv.id,\n" + " gv.goal_rule_id,\n" + " gv.goal_rule_detail_id,\n" +
    " gv.goal_type,\n" + " gv.check_object_id,\n" + " gv.check_level_field_api_name,\n" +
    " gr.check_level_field_api_name fields,\n" + " gr.check_level_type,\n" + " gv.fiscal_year\n" +
    " from goal_value gv left join goal_rule gr \n" + " on (gv.tenant_id=gr.tenant_id and gv.goal_rule_id=gr.id) \n" +
    " WHERE\n" + " gv.tenant_id=#{tenantId} \n" + " and gv.check_object_id=#{objectId} \n" +
    " and gr.check_level_type !='5'\n" + " and gr.check_level_type !='2' \n" + " and gr.check_level_type is not null" +
    " and gv.check_level_field_api_name is not null\n" + " and gv.goal_type=#{apiName}")
  List<Map<String, String>> getEffectRulesAllInfo(@Param("tenantId") String tenantId,
                                                  @Param("objectId") String objectId,
                                                  @Param("apiName") String apiName);

  @Select(
    "select count(1) from(\n" + " select\n" + " DISTINCT\n" + " gv.tenant_id,\n" + " gv.id,\n" + " gv.goal_rule_id,\n" +
    " gv.goal_rule_detail_id,\n" + " gv.goal_type,\n" + " gv.check_object_id,\n" + " gv.check_level_field_api_name,\n" +
    " gr.check_level_field_api_name fields,\n" + " gr.check_level_type,\n" + " gv.fiscal_year\n" +
    " from goal_value gv left join goal_rule gr \n" + " on(gv.tenant_id=gr.tenant_id and gv.goal_rule_id=gr.id) \n" +
    " WHERE\n" + " gv.tenant_id=#{tenantId} \n" + " and gv.check_object_id=#{objectId} \n" +
    " and gr.check_level_type !='5'\n" + " and gr.check_level_type !='2' \n" +
    " and gr.check_level_type is not null\n" + " and gv.check_level_field_api_name is not null\n" +
    " and gv.goal_type=#{apiName}) as t0")
  int getEffectRulesAllInfoCount(@Param("tenantId") String tenantId,
                                 @Param("objectId") String objectId,
                                 @Param("apiName") String apiName);

  //todo 省市区对象化是否有影响
  @Select("select t0.enum_code check_level_code_2,t1.enum_code check_level_code_1 from dim_sys_enum as t0 LEFT JOIN dim_sys_enum t1 on(t0.parent_id=t1.enum_id) where t0.enum_type_name='EnumCRMArea' AND t0.enum_code=#{cityId}")
  Map<String, Object> getCityInfo(@Param("cityId") String cityId);
}

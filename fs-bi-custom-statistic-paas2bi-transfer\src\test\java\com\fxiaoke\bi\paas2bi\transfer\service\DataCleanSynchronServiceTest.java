package com.fxiaoke.bi.paas2bi.transfer.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.paas2bi.transfer.bean.*;
import com.fxiaoke.bi.paas2bi.transfer.consumer.SchemaChangeConsumer;
import com.fxiaoke.bi.paas2bi.transfer.dao.PaasGoalRuleDao;
import com.fxiaoke.bi.paas2bi.transfer.mapper.bi.DataCleanSychonMapper;
import com.fxiaoke.bi.paas2bi.transfer.mapper.bi.GoalValueHandlerMapper;
import com.fxiaoke.bi.paas2bi.transfer.mapper.paas.GoalMapper;
import com.fxiaoke.bi.paas2bi.transfer.route.MyBatisRoutePolicy;
import com.fxiaoke.bi.paas2bi.transfer.utils.CommonsUtils;
import com.fxiaoke.bi.paas2bi.transfer.utils.SqlUtils;
import com.fxiaoke.jdbc.JdbcConnection;
import com.fxiaoke.metrics.CounterService;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2021/03/30
 * @Description
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext.xml")
public class DataCleanSynchronServiceTest {
  @Autowired
  DataCleanSynchronService dataCleanSynchronService;
  @Autowired
  DataCleanSychonMapper dataCleanSychonMapper;
  @Autowired
  SchemaChangeConsumer schemaChangeConsumer;
  @Autowired
  BIPGDataSource bipgDataSource;
  @Resource
  GoalMapper goalValueMapper;
  @Resource
  GoalValueHandlerMapper goalValueHandlerMapper;
  @Resource
  FiscalChangeService fiscalChangeService;
  @Resource
  FiscalDateConvertUtil fiscalDateConvertUtil;
  private FsGrayReleaseBiz gray = FsGrayRelease.getInstance("bi-statistic-offline-master");
  @Autowired
  CounterService counterService;
  @Autowired
  PaasGoalRuleDao paasGoalRuleDao;

  Map<String, RateLimiter> topicLimiter = Maps.newConcurrentMap();
  ;

  @BeforeClass
  public static void initConfig() throws Exception {
    //    System.setProperty("process.profile", "fstest");
  }

  @Test
  public void testSendNomon() {
    String json = "{\"meta\":{\"schema\":\"public\",\"op\":\"I\",\"time\":\"2022-12-22 17:00:22.307317\",\"db\":\"bi_112\",\"table\":\"product_category\"},\"keys\":{\"tenant_id\":\"71570\",\"id\":\"63a41c9f4118bd0001d92e4a\"},\"object_describe_api_name\":\"ProductCategoryObj\"}";
    OpLog oplog = JSON.parseObject(json, OpLog.class);
    dataCleanSynchronService.send2Gnomon(Lists.newArrayList(oplog));
  }

  @Test
  public void testEventCollector() {
    dataCleanSynchronService.dataCleanSync("89644");
  }

  @Test
  public void testFiscalETL() {
    String tenantId = "71570";
    String id = "61b2dfbb2c5b2d0001022f21";
    GoalValueEntity goalValueEntity = goalValueMapper.setTenantId(tenantId).getGoalValueById(tenantId, id);
    //    System.out.println(JSON.toJSONString(goalValueEntity));
    fiscalChangeService.etlGoalValueByRule(tenantId, Lists.newArrayList(goalValueEntity));
    //    fiscalChangeService.dealWithFiscalChange(tenantId);
  }

  @Test
  public void test1() {
    //    FiscalYear fiscalYear=fiscalChangeService.findFiscalConfig("71570","2019");
    //    System.out.println(JSON.toJSONString(fiscalYear));
    System.out.println(bipgDataSource.getPGMasterServer("71570"));
    System.out.println(bipgDataSource.getPGMasterServer("84622"));
  }

  @Test
  public void testNewCaiNian() throws Exception {
    String json = "{\"year\":\"2021\",\"fiscalMonths\":[{\"begin\":1609430400000,\"end\":1611504000000},{\"begin\":1611504000000,\"end\":1614182400000},{\"begin\":1614182400000,\"end\":1616601600000},{\"begin\":1616601600000,\"end\":1619280000000},{\"begin\":1619280000000,\"end\":1621872000000},{\"begin\":1621872000000,\"end\":1624550400000},{\"begin\":1624550400000,\"end\":1627142400000},{\"begin\":1627142400000,\"end\":1629820800000},{\"begin\":1629820800000,\"end\":1632499200000},{\"begin\":1632499200000,\"end\":1635091200000},{\"begin\":1635091200000,\"end\":1637769600000},{\"begin\":1637769600000,\"end\":1640966400000}]}";
    FiscalYear fiscalYear = JSON.parseObject(json, FiscalYear.class);

    String goalValueId = "61b6f75d47a7540001b9ab3b";
    String sql = SqlUtils.getGoalValue("71570", String.valueOf(goalValueId), false);
    System.out.println(sql);
    JdbcConnection BiJdbcConnection = bipgDataSource.getJdbcConnectionByTenantID("71570");
    BiJdbcConnection.query(sql, resultSet1 -> {
      GoalValue goalValue = CommonsUtils.getGoalValueObj(resultSet1, goalValueHandlerMapper, false, paasGoalRuleDao);
      List<GoalValueObj> goalValueObjInfos = CommonsUtils.getNewGoalValueObjArgList(goalValue);
      goalValueObjInfos.forEach(goalValueObjInfo -> {
        System.out.println(String.format("actionDate:%s,fiscalActionDate:%s,fiscalYear:%s,Month:%d,MonthValue:%s,FiscalYearMonth:%d", goalValueObjInfo.getAction_date(), goalValueObjInfo.getFiscal_action_date(), goalValueObjInfo.getFiscal_year(), goalValueObjInfo.getMonth(), String.valueOf(goalValueObjInfo.getMonth_value()), goalValueObjInfo.getFiscal_year_month()));
      });
    });
    BiJdbcConnection.close();
  }

  @Test
  public void testOplog() throws Exception {
    String msgBody = "{\"before\":{\"last_modified_time\":\"1639403848609\",\"annual_value\":\"78.00\",\"march_value\":\"3.00\",\"version\":\"2\"},\"meta\":{\"schema\":\"public\",\"op\":\"U\",\"time\":\"2021-12-14 15:47:33.558715\",\"db\":\"bi_112\",\"table\":\"goal_value\"},\"keys\":{\"tenant_id\":\"71570\",\"id\":\"61b2dfbb2c5b2d0001022f21\"},\"object_describe_api_name\":\"GoalValueObj\",\"after\":{\"last_modified_time\":\"1639468044264\",\"annual_value\":\"519.00\",\"march_value\":\"444.00\",\"version\":\"3\"}}";
    OpLog oplog = JSONObject.parseObject(msgBody, new TypeReference<OpLog>() {
    });
    //    GoalValueHandler goalValueHandler= new GoalValueHandler(bipgDataSource,  topicLimiter,  counterService,  goalValueHandlerMapper);
    //    goalValueHandler.handler(oplog);
  }


  @Test
  public void testRouterTransferMQ() throws Exception {
    System.out.println("start consumer ");
    //    System.out.println(bipgDataSource.getRouterInfo("71570").getJdbcUrl());
    //    Uninterruptibles.sleepUninterruptibly(50, TimeUnit.MINUTES);
    //    List<GoalValueEntity> goalValueEntities= goalValueMapper.setTenantId("71570").queryGoalValueByLimit("71570","0",10);
    //    goalValueEntities.forEach(goalValueEntity -> {
    //      System.out.println(JSON.toJSONString(goalValueEntity, SerializerFeature.WriteNullStringAsEmpty));
    //    });
    GoalValueEntity goalValueEntity = goalValueMapper.setTenantId("71570")
                                                     .getGoalValueById("71570", "5ec204f4d4418000016bb331");
    System.out.println(JSON.toJSONString(goalValueEntity));
  }

  @Test
  public void dataCleanSynchron() throws Exception {
 /*   String zz="e6069ce5603140d689ee6fb917839b4b";
    String parentPat="4f4b3e0d8511478eafb968ba8efaa90f.e6069ce5603140d689ee6fb917839b4b.53b24387842341af9ae7f21ed4e9b3c3";
    System.out.println(StringUtils.substring(parentPat,StringUtils.lastIndexOf(parentPat,zz),parentPat.length()));
*/
    //    dataCleanSynchronService.dataCleanSynchron("78557");
    //    dataCleanSynchronService.dataCleanSync("71570");
    //    dataCleanSychonMapper.setTenantId("71570").updateProductCategoryById("53", "'109.110.53'","71570","05198bf44e7a44c791fbdc002f03ed16");
    //    System.out.println("starting....");
    //    Uninterruptibles.sleepUninterruptibly(5, TimeUnit.HOURS);
    //    String eis="720724,663052,590059,720137,720136,712528,712633,663123,590062,683675,590058";
    FileWriter writer = new FileWriter(new File("C:\\Users\\<USER>\\Downloads\\product_catgory_etl_eis.txt"));
    BufferedReader reader = new BufferedReader(new FileReader(new File("C:\\Users\\<USER>\\Downloads\\query_result_uniq.txt")));
    String ei = null;
    while ((ei = reader.readLine()) != null) {
      if (!gray.isAllow("test_produce", ei)) {
        writer.write(ei + "\n");
      }
    }
    reader.close();
    writer.flush();
    writer.close();
  }

  @Test
  public void updateProductCategoryById() {
    //    dataCleanSynchronService.updateProductCategoryById("71570","e6069ce5603140d689ee6fb917839b4b");
    //    dataCleanSynchronService.updateProductCategoryByIdPlus("71570","5d5500693a13180001abb1f9");
    dataCleanSynchronService.updateProductCategoryByIdPlus("85145", "6656fb87a5bd7e0001392138");
  }

  @Test
  public void testInsert() {
    String json = "{\"meta\":{\"schema\":\"public\",\"op\":\"I\",\"time\":\"2021-06-09 13:52:58.655385\",\"db\":\"bi_112\",\"table\":\"product_category\"},\"keys\":{\"tenant_id\":\"71570\",\"id\":\"60c05738baee830001e4410b\"},\"object_describe_api_name\":\"ProductCategoryObj\"}";
    OpLog oplog = JSONObject.parseObject(json, new TypeReference<OpLog>() {
    });
    dataCleanSynchronService.dataCleanSynchronMain(oplog);
  }

  @Test
  public void testCategoryPath() {
    String msg = "{\"tenantId\":\"730688\",\"biz\":\"BI\",\"dialect\":\"postgresql\",\"before\":{\"url\":\"***********************************************\",\"isSchema\":false},\"after\":{\"url\":\"*********************************************************************************\",\"isSchema\":true}}";
    JSONObject jsonObject = JSON.parseObject(msg);
    String tenantId = jsonObject.getString("tenantId");
    String biz = jsonObject.getString("biz");
    String dialect = jsonObject.getString("dialect");
    if (MyBatisRoutePolicy.BIZ.equalsIgnoreCase(biz) && MyBatisRoutePolicy.DIALECT.equals(dialect)) {
      System.out.println("tenantId------------->" + tenantId);
      System.out.println("sucess !!!!");
    }
  }

  @Test
  public void testFiscalDate() throws Exception {
    PaasFiscalConfig paasFiscalConfig = fiscalDateConvertUtil.getFiscalDateConfig("71570", "2021");
    System.out.println(JSON.toJSONString(paasFiscalConfig));
    PaasFiscalConfig paasFiscalConfig2 = fiscalDateConvertUtil.getFiscalDateConfig("71570", "2022");
    System.out.println(JSON.toJSONString(paasFiscalConfig2));
    PaasFiscalConfig paasFiscalConfig3 = fiscalDateConvertUtil.getFiscalDateConfig("71570", "2023");
    System.out.println(JSON.toJSONString(paasFiscalConfig3));
  }

}
package com.fxiaoke.bi.paas2bi.transfer.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.bi.metadata.context.utils.SysUdfMetadataUtil;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.bi.paas2bi.transfer.bean.OpLog;
import com.fxiaoke.bi.paas2bi.transfer.bean.PageInfo;
import com.fxiaoke.bi.paas2bi.transfer.dao.GoalValueDao;
import com.fxiaoke.bi.paas2bi.transfer.mapper.paas.GoalMapper;
import com.fxiaoke.bi.paas2bi.transfer.pojo.GoalValuePlus;
import com.fxiaoke.bi.paas2bi.transfer.service.PaasPgDataSource;
import com.fxiaoke.bi.paas2bi.transfer.utils.CommonsUtils;
import com.fxiaoke.bi.statistic.common.utils.ObjectConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class NewGoalValueUpdateHandler extends BatchHandler {

  private final GoalValueDao goalValueDao;
  private final PaasPgDataSource paasPgDataSource;
  private final GoalMapper goalMapper;
  private final EIEAConverter eieaConverter;

  public NewGoalValueUpdateHandler(GoalValueDao goalValueDao,
                                   PaasPgDataSource paasPgDataSource,
                                   GoalMapper goalMapper,
                                   @Autowired(required = false) EIEAConverter eieaConverter) {
    this.goalValueDao = goalValueDao;
    this.paasPgDataSource = paasPgDataSource;
    this.goalMapper = goalMapper;
    this.eieaConverter = eieaConverter;
  }

  @Override
  public void batchHandler(List<OpLog> opLogs) {
    for (OpLog opLog : opLogs) {
      String kind = opLog.getKind();
      if ("update".equals(kind)) {
        String apiName = opLog.getObject_describe_api_name();
        String tenantId = opLog.getTenantId();
        CommonsUtils.createTrace(eieaConverter, tenantId, ObjectId.get().toString());
        String tableName = opLog.getTable();
        // 拿到id后根据考核的类型id去看那些goal_value变化了，找到这些goal_value更新到month, week, quarter, year这些表中
        String objectId = opLog.findPrimKey();
        if (StringUtils.isBlank(objectId)) {
          log.warn("can not get id opLog:{}", JSON.toJSONString(opLog));
          continue;
        }

        //对于员工的目标单独处理
        if ("org_employee_user".equals(tableName)) {
          apiName = "2";
          objectId = opLog.getUserId();
        }
        //兼容变化的对象是老对象的扩展对象。//因为agg_effect_field对于预制对象，无论是预制字段还是扩展字段，apiname存储的都是不带_udef的。
        if (StringUtils.isNotBlank(apiName) && apiName.endsWith("_udef")) {
          String apiNameSplit = ObjectConfigManager.getPreObjName(apiName);
          apiName = SysUdfMetadataUtil.getCrmObjNameByBiObjName(apiNameSplit);
        }
        boolean isSchema = paasPgDataSource.isSchema(tenantId);
        // 查找受影响的rule和value
        List<GoalValuePlus> allEffectGoalValues = goalMapper.setTenantId(tenantId)
                                                            .getAllEffectGoalValues(tenantId, objectId, apiName);
        PageInfo<GoalValuePlus> pageInfo = new PageInfo<>(allEffectGoalValues, "0", 50);
        goalValueDao.updateCheckLevel(tenantId, pageInfo, isSchema);
      }
    }
  }
}
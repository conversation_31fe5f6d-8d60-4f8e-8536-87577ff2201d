package com.fxiaoke.bi.paas2bi.transfer.service;

import com.fxiaoke.bi.paas2bi.transfer.bean.OpLog;
import com.fxiaoke.bi.paas2bi.transfer.dispatcher.listener.MultiLangRecordHandler;
import com.fxiaoke.bi.paas2bi.transfer.utils.GrayManagerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 多语言记录监听器
 *
 * <AUTHOR> zzh
 * @createTime : [2025/3/31 18:16]
 */
@Slf4j
@Component
public class MultiLangRecordListener implements MessageListenerConcurrently {

  private final MultiLangRecordHandler multiLangRecordHandler;
  private final PaasPgDataSource paasPgDataSource;

  public MultiLangRecordListener(MultiLangRecordHandler multiLangRecordHandler, PaasPgDataSource paasPgDataSource) {
    this.multiLangRecordHandler = multiLangRecordHandler;
    this.paasPgDataSource = paasPgDataSource;
  }

  @Override
  public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
    // 创建消息监听器适配器
    List<OpLog> multiLangOpLogs = new ArrayList<>();

    for (MessageExt msg : msgs) {
      String topic = msg.getTopic();
      String tags = msg.getTags();
      try {
        // 过滤非多语言表消息
        if (!isMultiLangMessage(tags)) {
          log.debug("跳过非多语言表消息, topic:{}, tags:{}, msgId:{}", topic, tags, msg.getMsgId());
          continue;
        }
        OpLog opLog = OpLog.of(msg);
        String tenantId = opLog.getTenantId();
        // 灰度配置和路由校验
        if (skipOrNot(tenantId, opLog)) {
          log.debug("跳过消息处理, tenantId:{}, table:{}, msgId:{}", tenantId, opLog.getTable(), msg.getMsgId());
          continue;
        }
        if ("insert".equals(opLog.getKind())) {
          multiLangOpLogs.add(opLog);
        }
        log.debug("添加多语言表消息到处理队列, tenantId:{}, table:{}, msgId:{}", tenantId, opLog.getTable(), msg.getMsgId());
      } catch (Exception e) {
        log.error("解析多语言表消息失败, topic:{}, msgId:{}", topic, msg.getMsgId(), e);
      }
    }

    // 批量处理多语言表消息
    if (CollectionUtils.isNotEmpty(multiLangOpLogs)) {
      try {
        multiLangRecordHandler.onTrigger(multiLangOpLogs);
        log.debug("成功处理 {} 条多语言表消息", multiLangOpLogs.size());
      } catch (Exception e) {
        log.error("处理多语言表消息失败, 消息数量:{}", multiLangOpLogs.size(), e);
      }
    }
    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
  }

  /**
   * 判断是否为多语言表消息
   *
   * @param tags 消息标签
   * @return true 如果是多语言表消息
   */
  private boolean isMultiLangMessage(String tags) {
    return tags != null && tags.endsWith("_lang");
  }

  /**
   * 是否跳过计算
   *
   * @param tenantId 租户ID
   * @param opLog    操作日志
   * @return true 如果需要跳过
   */
  private boolean skipOrNot(String tenantId, OpLog opLog) {
    if (tenantId == null || !GrayManagerUtil.paas2bi(tenantId)) {
      log.warn("tenantId is null or is in black list skip ETL!, tenantId:{}", tenantId);
      return true;
    }

    // 路由校验 只对线上的路由进行校验
    return paasPgDataSource.checkRoute(opLog);
  }
}

package com.fxiaoke.bi.paas2bi.transfer.dispatcher.listener;

import com.fxiaoke.bi.paas2bi.transfer.bean.OpLog;
import com.fxiaoke.bi.paas2bi.transfer.multilangrecord.MultiLangService;
import com.fxiaoke.bi.statistic.common.utils.GrayManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 多语言记录监听器
 *
 * <AUTHOR> zzh
 * @createTime : [2025/3/31 18:16]
 */
@Service
@Slf4j
public class MultiLangRecordHandler implements EventListener<OpLog> {

  private final MultiLangService multiLangService;

  public MultiLangRecordHandler(MultiLangService multiLangService) {
    this.multiLangService = multiLangService;
  }

  public void onTrigger(List<OpLog> events) {
    Map<String, List<OpLog>> tenantIdTableListMap = events.stream()
                                                          .filter(Objects::nonNull)
                                                          .collect(Collectors.groupingBy(OpLog::getTenantId));
    for (Map.Entry<String, List<OpLog>> entry : tenantIdTableListMap.entrySet()) {
      String tenantId = entry.getKey();
      if (GrayManager.isAllowByRule("skip_record_lang_obj_ei", tenantId)) {
        continue;
      }
      List<OpLog> opLogList = entry.getValue();
      Collection<OpLog> distinctOpLogs = opLogList.stream()
                                                  // 按照 tenantId|apiName 进行分组，并使用第一个元素覆盖重复的元素
                                                  .collect(Collectors.toMap(opLog -> String.format("%s|%s", opLog.getTenantId(), opLog.getObject_describe_api_name()), Function.identity(), (existing, replacement) -> replacement))
                                                  .values();

      // 过滤掉需要跳过的表
      List<OpLog> validOpLogs = distinctOpLogs.stream()
                                              .filter(opLog -> !GrayManager.isAllowByRule("skip_record_lang_obj_table", opLog.getTable()))
                                              .collect(Collectors.toList());

      if (validOpLogs.isEmpty()) {
        log.debug("没有需要处理的多语言记录, tenantId: {}", tenantId);
        continue;
      }

      try {
        // 使用批量处理服务
        multiLangService.processMultiLangRecordsBatch(tenantId, validOpLogs);
        log.debug("批量处理多语言记录完成, tenantId: {}, count: {}", tenantId, validOpLogs.size());
      } catch (Exception e) {
        log.error("批量处理多语言记录异常, tenantId: {}, count: {}", tenantId, validOpLogs.size(), e);
        // 如果批量处理失败，回退到单条处理模式
        log.info("回退到单条处理模式, tenantId: {}", tenantId);
        for (OpLog opLog : validOpLogs) {
          try {
            // 使用原有的单条处理逻辑
            multiLangService.processMultiLangRecord(tenantId, opLog);
          } catch (Exception singleException) {
            log.error("处理多语言记录异常, tenantId:{}, table:{}, apiName:{}, msgId:{}", tenantId, opLog.getTable(), opLog.getObject_describe_api_name(), opLog.getMsgId(), singleException);
          }
        }
      }
    }
  }
}

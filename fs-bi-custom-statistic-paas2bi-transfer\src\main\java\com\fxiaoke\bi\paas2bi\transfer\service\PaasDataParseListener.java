package com.fxiaoke.bi.paas2bi.transfer.service;

import com.fxiaoke.bi.paas2bi.transfer.bean.OpLog;
import com.fxiaoke.bi.paas2bi.transfer.dispatcher.listener.*;
import com.fxiaoke.bi.paas2bi.transfer.dispatcher.manger.DispatcherManager;
import com.fxiaoke.bi.paas2bi.transfer.utils.CommonsUtils;
import com.fxiaoke.bi.paas2bi.transfer.utils.GrayManagerUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by jief on 2019/9/24.
 * 处理除多语言表以外的所有消息，多语言表消息已迁移到 MultiLangRecordConsumer 处理
 */
@Slf4j
@Component
public class PaasDataParseListener implements MessageListenerConcurrently {

  @Autowired
  private PaasPgDataSource paasPgDataSource;
  @Getter
  @Resource
  private CheckLevelFieldListener checkLevelFieldListener;
  @Getter
  @Resource
  private AreaInfoEventListener areaInfoEventListener;
  @Getter
  @Resource
  private GoalRuleEventListener goalRuleEventListener;
  @Getter
  @Resource
  private GoalValueEventListener goalValueEventListener;
  @Getter
  @Resource
  private ProductCategoryEventListener productCategoryEventListener;
  @Getter
  @Resource
  private MultiDimGoalEventListener multiDimGoalEventListener;
  @Getter
  @Resource
  private MultiLangRecordHandler multiLangRecordHandler;

  @Override
  public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
    DispatcherManager dispatcherManager = DispatcherManager.createInstance(this);
    for (MessageExt msg : msgs) {
      String topic = msg.getTopic();
      //过滤agg_data的数据或则过滤指定tag
      String tags = msg.getTags();
      if (tags.startsWith("agg_") || CommonsUtils.stop_tags.contains(tags)) {
        continue;
      }
      try {
        OpLog oplog = OpLog.of(msg);
        String tenantId = oplog.getTenantId();
        //灰度配置和tag校验
        if (this.skipOrNot(tenantId, oplog)) {
          continue;
        }
        dispatcherManager.appendEvent(oplog);
      } catch (Exception e) {
        log.error("parse msg error topic:{}, msgId: {}", topic, msg.getMsgId(), e);
      }
    }
    try {
      dispatcherManager.runEvents();
    } catch (Exception e) {
      log.error("dispatcherManager run events error", e);
      return ConsumeConcurrentlyStatus.RECONSUME_LATER;
    }
    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
  }


  /**
   * 是否跳过计算
   */
  public boolean skipOrNot(String tenantId, OpLog oplog) {
    if (tenantId == null || !GrayManagerUtil.paas2bi(tenantId) || "-1".equals(tenantId)) {
      log.warn("tenantId is null or is in black list skip ETL!,{}", tenantId);
      return true;
    }
    //路由校验 只对线上的路由进行校验
    return paasPgDataSource.checkRoute(oplog);
  }
}

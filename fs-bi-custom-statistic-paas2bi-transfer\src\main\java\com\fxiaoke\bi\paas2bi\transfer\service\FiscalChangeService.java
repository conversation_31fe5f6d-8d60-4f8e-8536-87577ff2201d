package com.fxiaoke.bi.paas2bi.transfer.service;

import com.fxiaoke.bi.paas2bi.transfer.bean.*;
import com.fxiaoke.bi.paas2bi.transfer.mapper.bi.GoalValueObjMapper;
import com.fxiaoke.bi.paas2bi.transfer.mapper.paas.GoalMapper;
import com.fxiaoke.bi.paas2bi.transfer.threadpool.ExecutorPoolService;
import com.fxiaoke.bi.paas2bi.transfer.utils.CommonsUtils;
import com.fxiaoke.bi.paas2bi.transfer.utils.Context;
import com.fxiaoke.bi.paas2bi.transfer.utils.PrepareStatementExecutor;
import com.fxiaoke.bi.statistic.common.utils.GrayManager;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.autoconf.ConfigFactory;
import com.github.mybatis.util.InjectSchemaUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 财年变更重新计算所有
 * 目标值的财月的起始日期。
 * 数据量超大
 */
@Slf4j
@Service
public class FiscalChangeService {

  @Resource
  GoalMapper goalMapper;
  @Resource
  GoalValueObjMapper goalValueObjMapper;
  @Resource
  BIPGDataSource bipgDataSource;
  @Resource
  FiscalDateConvertUtil fiscalDateConvertUtil;
  @Resource
  ExecutorPoolService executorPoolService;

  private volatile int batchSize = 72;
  private volatile int pageSize = 40;
  private static final DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyyMMdd");

  private static final String updateSQL =
    "UPDATE goal_value_obj SET " + "fiscal_year_month=?," + "fiscal_year=?," + "month_value=?," + "action_date=?," +
    "fiscal_action_date=?," + "month=?," + "last_modified_time=? WHERE tenant_id=? AND id=? ";

  private static final String updateAggDateSQL = "UPDATE agg_data SET " + "fiscal_action_date=?," +
                                                 "last_modified_time=? WHERE tenant_id=? and object_describe_api_name='goal_value_obj' AND object_id=? and action_date=?";

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-paas2bi-transfer_goal_mq", IConfig -> {
      batchSize = IConfig.getInt("update_batchSize", 120);
      pageSize = IConfig.getInt("goal_value_page_size", 40);
    });
  }

  /**
   * 异步批量清洗企业的财年变更
   *
   * @param tenantIds 企业id
   */
  public void asyncEtlFiscalChange(List<String> tenantIds) {
    executorPoolService.submitOrWait(() -> {
      tenantIds.forEach(this::dealWithFiscalChange);
    });
  }

  /**
   * 处理财年变更计算全量的goal_value 数据
   *
   * @param tenantId 企业id
   */
  public void dealWithFiscalChange(String tenantId) {
    StopWatch stopWatch = new StopWatch("etl_goal_value");
    stopWatch.start("etl");
    PageInfo startPageInfo = PageInfo.builder().pageSize(this.pageSize).startId("0").build();
    long counter = 0L;
    try {
      while (StringUtils.isNotBlank(startPageInfo.getStartId())) {
        List<GoalValueEntity> goalValueEntities = goalMapper.setTenantId(tenantId)
                                                            .queryGoalValueByLimit(tenantId, startPageInfo.getStartId(), startPageInfo.getPageSize());
        PageInfo nextPageInfo = this.etlGoalValueByRule(tenantId, goalValueEntities);
        if (nextPageInfo != null && nextPageInfo.getPageSize() == startPageInfo.getPageSize()) {
          startPageInfo.setStartId(nextPageInfo.getStartId());
        } else {
          startPageInfo.setStartId(null);
        }
        if (nextPageInfo != null) {
          counter = counter + nextPageInfo.getPageSize();
        }
        log.info("etl goal value tenantId:{},count:{},next ruleId:{}", tenantId, counter, startPageInfo.getStartId());
      }
    } finally {
      stopWatch.stop();
    }
    log.info("etl goal value complete tenantId:{},etl:{}rows,cost:{}ms", tenantId, counter, stopWatch.getTotalTimeMillis());
  }

  /**
   * 批量清洗goal_value_obj
   *
   * @param tenantId          企业id
   * @param goalValueEntities 目标值bean
   * @return 当前分页信息
   */
  public PageInfo etlGoalValueByRule(String tenantId, List<GoalValueEntity> goalValueEntities) {
    if (CollectionUtils.isEmpty(goalValueEntities)) {
      return PageInfo.builder().pageSize(0).startId(null).build();
    }
    PageInfo pageInfo = PageInfo.builder().pageSize(goalValueEntities.size()).startId(null).build();
    List<String> ids = Lists.newArrayList();
    Map<String, GoalValueEntity> goalValueEntityMap = Maps.newHashMap();
    goalValueEntities.forEach(goalValueEntity -> {
      ids.add(goalValueEntity.getId());
      pageInfo.setStartId(goalValueEntity.getId());
      goalValueEntityMap.put(goalValueEntity.getId(), goalValueEntity);
    });
    List<GoalValueObjInfo> goalValueObjInfos = goalValueObjMapper.setTenantId(tenantId)
                                                                 .queryGoalValueObjByOIds(tenantId, ids);
    GoalValueObjMaps goalValueObjMaps = GoalValueObjMaps.getInstance(tenantId);
    if (CollectionUtils.isNotEmpty(goalValueObjInfos)) {
      goalValueObjInfos.forEach(goalValueObjMaps::append);
    }
    //用当前goal value obj 数据和最新的 goal value obj 数据对比后生成 批量 update 操作
    try (JdbcConnection jdbcConnection = bipgDataSource.getJdbcConnectionByTenantID(tenantId); JdbcConnection jdbcConnectionAggData = bipgDataSource.getJdbcConnectionByTenantID(tenantId)) {
      String sql = updateSQL;
      if (bipgDataSource.isSchema(tenantId)) {
        sql = InjectSchemaUtil.injectSchema(updateSQL, "postgresql", "sch_" + tenantId);
      }
      String aggDataSQL = updateAggDateSQL;
      if (bipgDataSource.isSchema(tenantId)) {
        aggDataSQL = InjectSchemaUtil.injectSchema(aggDataSQL, "postgresql", "sch_" + tenantId);
      }
      PrepareStatementExecutor aggDataPrepareStatementExecutor = new PrepareStatementExecutor("aggDataUpdate", jdbcConnectionAggData.connection(), aggDataSQL, this.batchSize);
      PrepareStatementExecutor prepareStatementExecutor = new PrepareStatementExecutor("paa2bi_transfer", jdbcConnection.connection(), sql, this.batchSize);
      goalValueObjMaps.forEach((oid, goalValueObjList) -> {
        try {
          int fiscalMonthsSize = goalValueObjList.size();
          if (fiscalMonthsSize != 12) {
            log.error("goal value obj data not 12 tenantId:{},months id={}", tenantId, oid);
            return;
          }
          GoalValueEntity goalValueEntity = goalValueEntityMap.get(oid);
          FiscalYear fiscalYear = this.findFiscalConfig(tenantId, goalValueEntity.getFiscalYear());
          if (fiscalYear == null) {
            log.error("can not findFiscalConfig tenantId:{},fiscalYear:{}", tenantId, goalValueEntity.getFiscalYear());
            return;
          }
          List<GoalValueObjInfo> newGoalValueObjList = this.createNewGoalValueObj(goalValueEntity, fiscalYear);
          Iterator<GoalValueObjInfo> goalValueObjInfoIterator = goalValueObjList.iterator();
          for (GoalValueObjInfo newGoalValueObjInfo : newGoalValueObjList) {
            if (goalValueObjInfoIterator.hasNext()) {
              GoalValueObjInfo oldGoalValueObjInfo = goalValueObjInfoIterator.next();
              //对比如果不同则更新
              if (!newGoalValueObjInfo.compareDateAndValue(oldGoalValueObjInfo)) {
                newGoalValueObjInfo.setId(oldGoalValueObjInfo.getId());
                prepareStatementExecutor.accept(newGoalValueObjInfo.getUpdateValues());
              }
              if (StringUtils.equalsAny(newGoalValueObjInfo.getGoalType(), "1", "2")) {
                //强制更新agg_data 表
                aggDataPrepareStatementExecutor.accept(newGoalValueObjInfo.createUpdateAggDataValues());
              }
            }
          }
        } catch (Exception e) {
          log.error("etl goalValue error tenantId:{},id:{}", tenantId, oid, e);
          throw new RuntimeException(e);
        }
      });
      prepareStatementExecutor.flush();
      aggDataPrepareStatementExecutor.flush();
    } catch (Exception e) {
      log.error("compare and update error tenantId:{}", tenantId, e);
      throw new RuntimeException(e);
    }
    return pageInfo;
  }

  /**
   * 创建新财年数据集
   *
   * @param goalValue  goal_value 数据
   * @param fiscalYear 财年信息
   * @return List<GoalValueObjInfo>
   */
  public List<GoalValueObjInfo> createNewGoalValueObj(GoalValueEntity goalValue, FiscalYear fiscalYear) {
    String goalValueName;
    if (StringUtils.isBlank(goalValue.getGoalRuleDetailId())) {
      goalValueName = String.format("%s_%s_%s_%s", goalValue.getCheckObjectId(), goalValue.getGoalType(), goalValue.getGoalRuleId(), "nogoaldetail");
    } else {
      goalValueName = String.format("%s_%s_%s_%s", goalValue.getCheckObjectId(), goalValue.getGoalType(), goalValue.getGoalRuleId(), goalValue.getGoalRuleDetailId());
    }
    String goalType = goalValue.getGoalType();
    List<GoalValueObjInfo> goalValueObjInfos = Lists.newArrayList();
    Map<String, BigDecimal> monthValueMap = goalValue.getMonthValueMap();
    int startMonth = fiscalYear.findStartMonth();
    int startYear = fiscalYear.findStartYear();
    LocalDate oldFiscalLocalDate = LocalDate.of(startYear, startMonth, 1);
    for (int i = 1; i <= 12; i++) {
      FiscalYear.FiscalMonth fiscalMonth = fiscalYear.getFiscalMonthByMonth(i);
      String actionDate = oldFiscalLocalDate.atStartOfDay().format(dateFormat);
      String fiscalActionDate = fiscalMonth.findBeginDate();
      int month = oldFiscalLocalDate.getMonthValue();
      int year = oldFiscalLocalDate.getYear();
      int currentMonth = startMonth++ % 12;
      if (currentMonth == 0) {
        currentMonth = 12;
      }
      BigDecimal monthValue = monthValueMap.get(String.format("%02d", currentMonth));
      String timeZone = null;
      if (GrayManager.supportMultipleTimeZone(goalValue.getTenantId())) {
        timeZone = Context.ShangHai_ZONE;
      }
      GoalValueObjInfo goalValueObjInfo = GoalValueObjInfo.builder()
                                                          .tenantId(goalValue.getTenantId())
                                                          .oid(goalValue.getId())
                                                          .goalRuleId(goalValue.getGoalRuleId())
                                                          .goalRuleDetailId(goalValue.getGoalRuleDetailId())
                                                          .fiscalYear(String.valueOf(year))
                                                          .actionDate(actionDate)
                                                          .fiscalActionDate(fiscalActionDate)
                                                          .month(month)
                                                          .fiscalYearMonth(CommonsUtils.dateToStamp2(actionDate, timeZone))
                                                          .monthValue(monthValue)
                                                          .ruleId(StringUtils.isNotBlank(goalValue.getGoalRuleDetailId()) ?
                                                            goalValue.getGoalRuleDetailId() :
                                                            goalValue.getGoalRuleId())
                                                          .goalValueName(goalValueName)
                                                          .goalType(goalType)
                                                          .build();
      goalValueObjInfos.add(goalValueObjInfo);
      oldFiscalLocalDate = oldFiscalLocalDate.plusMonths(1L);
    }
    return goalValueObjInfos;
  }

  /**
   * 根据企业id和财年获取所有财月的起止时间
   *
   * @param tenantId tenant_id
   * @param year     2021
   * @return FiscalYear
   */
  public FiscalYear findFiscalConfig(String tenantId, String year) {
    try {
      PaasFiscalConfig paasFiscalConfig = fiscalDateConvertUtil.getFiscalDateConfig(tenantId, year);
      return paasFiscalConfig.getCurrentYear();
    } catch (Exception e) {
      log.error("get fiscal error tenantId:{},year:{}", tenantId, year, e);
    }
    //    Map<String, List<FiscalYear.FiscalMonth>> fiscalMonthMap = this.tenantFiscalYearMap.get(tenantId);
    //    if (fiscalMonthMap != null) {
    //      List<FiscalYear.FiscalMonth> fiscalMonths = fiscalMonthMap.get(year);
    //      if (fiscalMonths != null) {
    //        FiscalYear fiscalYear = new FiscalYear();
    //        fiscalYear.setYear(year);
    //        fiscalYear.setFiscalMonths(fiscalMonths);
    //        return fiscalYear;
    //      }
    //    }
    return null;
  }

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class PageInfo {
    private int pageSize;
    private String startId;
  }
}

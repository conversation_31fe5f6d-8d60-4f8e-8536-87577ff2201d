package com.fxiaoke.bi.paas2bi.transfer.bean;

import lombok.Data;

import java.util.List;

@Data
public class SysOffLineArg {

  private String tenantId;
  private String goalValueId;
  private String goalRuleId;
  private List<String> goalRuleIds;
  private String Ip;
  private String port;
  private String dbs;
  private List<String> tenantIds;
  private List<String> goalValueIds;
  private List<String> tableNames;

  /**
   * 是否强制刷指标检测数据 true 则强制刷
   */
  private Boolean mandatory = false;

  /**
   * 国家省市区国家 id
   */
  private List<String> mtAreaIds;

  private String tableName;

  /**
   * 开启或关闭多语言同步<br>
   * start: 开启<br>
   * close: 关闭
   */
  private String startOrClose;
}

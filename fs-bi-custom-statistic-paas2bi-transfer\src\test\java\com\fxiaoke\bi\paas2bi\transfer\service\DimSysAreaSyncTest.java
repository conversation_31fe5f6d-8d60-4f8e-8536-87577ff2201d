package com.fxiaoke.bi.paas2bi.transfer.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.paas2bi.transfer.areasync.bean.MtCountryAreaInfo;
import com.fxiaoke.bi.paas2bi.transfer.areasync.dao.AreaInfoDao;
import com.fxiaoke.bi.paas2bi.transfer.areasync.service.AreaInfoIncSyncService;
import com.fxiaoke.bi.paas2bi.transfer.areasync.service.DimSysAreaService;
import com.fxiaoke.bi.paas2bi.transfer.areasync.service.MtCountryAreaInfoService;
import com.fxiaoke.bi.paas2bi.transfer.areasync.utils.AreaCommonUtils;
import com.fxiaoke.bi.paas2bi.transfer.bean.OpLog;
import com.fxiaoke.bi.statistic.common.dbscan.DBInfo;
import com.fxiaoke.bi.statistic.common.dbscan.DBInfoService;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fxiaoke.bi.paas2bi.transfer.areasync.utils.AreaCommonUtils.*;

/**
 * <AUTHOR> Zhenghao
 * @date 2022/8/10
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext.xml")
public class DimSysAreaSyncTest {

  @Autowired
  AreaInfoDao areaInfoDao;
  @Autowired
  AreaInfoIncSyncService areaInfoIncSyncService;
  @Autowired
  BIPGDataSource bipgDataSource;
  @Autowired
  MtCountryAreaInfoService mtCountryAreaInfoService;
  @Autowired
  DBInfoService dbInfoService;
  @Autowired
  DimSysAreaService dimSysAreaService;

  @Test
  public void testGetPaasData() {
    List<MtCountryAreaInfo> mtAreaDimPublic = mtCountryAreaInfoService.getMtAreaDimPublic("86369", "0");
    System.out.println("(mtAreaDimPublic) = " + JSON.toJSONString(mtAreaDimPublic));
  }

  @Test
  public void testAreaSysInfoByIds() {
    Map<String, List<String>> dbInfo = dbInfoService.getDBInfoFromRemote(schemaUrl, DIALECT, BIZ);
    HashMap<String, List<String>> dbInfoMap = new HashMap<>();
    dbInfoMap.put("***********:5432", dbInfo.get("***********:5432"));
    areaInfoIncSyncService.etlMtToDimByIds(Lists.newArrayList("62b028869f788a0001cc31c0"), dbInfoMap);
  }

  @BeforeClass
  public static void initConfig() {
    System.setProperty("process.profile", "fstest");
  }

  @Test
  public void testEtlSysNewColumn() {
    areaInfoDao.etlSysAreaNewColumn("*******************************************", "public");
  }

  @Test
  public void testEtlByTenantId() {
    areaInfoDao.etlAreaInfoByTenantId("85529");
  }

  @Test
  public void testAreaInfoDao() {
    areaInfoDao.etlByDbUrl("77996");
  }

  @Test
  public void testAreaInfoDaoPublic() {
    areaInfoDao.etlByDbUrl("*******************************************", "bi_112");
  }

  @Test
  public void testGetDbIpName() {
    List<DBInfo> infoListFromRemote = dbInfoService.getDBInfoListFromRemote(AreaCommonUtils.schemaUrl, AreaCommonUtils.DIALECT, AreaCommonUtils.BIZ);
    infoListFromRemote.forEach(System.out::println);
  }

  @Test
  public void testGetCount() {
    Long areaRows = dimSysAreaService.countSysSchemaDimAreaRows("82958");
    System.out.println("areaRows = " + areaRows);
    Long bi112 = areaInfoDao.countSysPublicDimAreaRows("*******************************************");
    System.out.println("bi112 = " + bi112);
    Long paasSysMtAreaRows = mtCountryAreaInfoService.getPaasSysMtAreaRows();
    System.out.println("paasSysMtAreaRows = " + paasSysMtAreaRows);
  }

  @Test
  public void testSysAreaInfoDao() {
    areaInfoDao.etlSysAreaInfoByTenantId("82958");
  }

  @Test
  public void testGetMtSysByCodes() {
    List<MtCountryAreaInfo> mtAreaPaasSysByCodes = mtCountryAreaInfoService.getMtAreaPaasSysByCodes(new String[] {"248", "273", "550", "2969", "38231"});
    String[] names = mtAreaPaasSysByCodes.stream().map(MtCountryAreaInfo::getName).toArray(String[]::new);
    System.out.println("names = " + Arrays.toString(names));
  }

  @Test
  public void testGetMtSysByIds() {
    List<MtCountryAreaInfo> mtAreaPaasSysByIds = mtCountryAreaInfoService.getMtAreaPaasSysByIds(new String[] {"6335052e4616c2000182f99a", "633505314616c2000182f9a4", "633505384616c2000182f9c3", "6335053f4616c2000182f9e2"});
    System.out.println("mtAreaPaasSysByIds = " + JSON.toJSONString(mtAreaPaasSysByIds));
  }

  @Test
  public void testDealOpLog() {
    // String opLog = "{\"before\":{\"name\":\"八里营镇\"},\"meta\":{\"schema\":\"public\",\"op\":\"U\",\"time\":\"2022-08-13 06:40:15.004611\",\"db\":\"bi_112\",\"table\":\"mt_country_area_info\"},\"keys\":{\"tenant_id\":\"78057\",\"id\":\"62b9a95542908c358caa83e6\"},\"after\":{\"name\":\"八里营乡\"}}";
    // String opLog = "{\"meta\":{\"schema\":\"sch_719279\",\"op\":\"I\",\"time\":\"2022-11-18 23:14:08.980282\",\"db\":\"fsbidb014083001\",\"table\":\"mt_country_area_info\"},\"keys\":{\"tenant_id\":\"78057\",\"id\":\"62660518693dcd00014387d8\"}}";
    // String opLog = "{\"meta\":{\"schema\":\"public\",\"op\":\"I\",\"time\":\"2022-11-18 16:02:29.805384\",\"db\":\"bi_112\",\"table\":\"mt_country_area_info\"},\"keys\":{\"tenant_id\":\"71570\",\"id\":\"62660518693dcd00014387d8\"}}";
    // String opLog = "{\"meta\":{\"schema\":\"public\",\"op\":\"I\",\"time\":\"2022-11-21 10:18:16.379528\",\"db\":\"bi_112\",\"table\":\"mt_country_area_info\"},\"keys\":{\"tenant_id\":\"78057\",\"id\":\"6263b639519254000173c05e\"}}";
    String insertOpLog = "{\"meta\":{\"schema\":\"public\",\"op\":\"I\",\"time\":\"2022-12-02 14:22:53.389412\",\"db\":\"bi_112\",\"table\":\"mt_country_area_info\"},\"keys\":{\"tenant_id\":\"71570\",\"id\":\"62ce9a99c47f5000015e9f70\"}}";
    OpLog opLog1 = JSON.parseObject(insertOpLog, OpLog.class);
    areaInfoIncSyncService.dealOpLogs(Lists.newArrayList(opLog1));
  }

  @Test
  public void testEtlAreaByDb() {
    String param = "{\"***********:5432\":[\"sch_bi_112\",\"fsdb112003001\",\"sch_paas_112\"],\"*************:5432\":[\"bi_112\"]}";
    Map<String, List<String>> map = JSON.parseObject(param, new TypeReference<Map<String, List<String>>>() {});
    areaInfoIncSyncService.triggerDimAreaByDb(map);
  }

  @Test
  public void testPgUrl() throws SQLException {
    String pgMasterServer = bipgDataSource.getPGMasterServer("82958");
    Connection connection = bipgDataSource.getConnection(pgMasterServer);
    System.out.println("connection = " + (connection));
    JdbcConnection jdbcConnectionByTenantID = bipgDataSource.getJdbcConnectionByTenantID("82958");
    Connection connection1 = jdbcConnectionByTenantID.connection();
    System.out.println("connection1 = " + (connection1));
  }

  @Test
  public void testSplit() {
    String breadName = "ALL/Austria/Niederösterreich/Politischer Bezirk Melk/Hofamt Priel/Kalz/Reith";
    String[] split = StringUtils.split(breadName, "/");
    System.out.println("split = " + Arrays.toString(split));
  }

  @Test
  public void testSyncTenantData() {
    List<MtCountryAreaInfo> mtAreaPaasPublic = mtCountryAreaInfoService.getMtAreaPaasPublic("*******************************************", 500, "61efdac8a15601faec378f5d");
    System.out.println("mtAreaPaasPublic = " + mtAreaPaasPublic.size());
  }
}

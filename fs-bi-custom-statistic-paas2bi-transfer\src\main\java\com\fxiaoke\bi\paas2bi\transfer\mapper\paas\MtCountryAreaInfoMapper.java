package com.fxiaoke.bi.paas2bi.transfer.mapper.paas;

import com.fxiaoke.bi.paas2bi.transfer.areasync.bean.MtCountryAreaInfo;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ICrudPaginationMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * bi的mt_country_area_info
 *
 * @Entity {@link com.fxiaoke.bi.paas2bi.transfer.areasync.bean.MtCountryAreaInfo}
 */
@Repository
public interface MtCountryAreaInfoMapper extends ICrudPaginationMapper<MtCountryAreaInfo>, IBatchMapper<MtCountryAreaInfo>, ITenant<MtCountryAreaInfoMapper> {

  /**
   * 根据tenant_id分页查询地区信息
   */
  @Select(
    "SELECT id, fs_unique_code, name, type, tenant_id, parent_fs_unique_code, latitude, longitude, breadcrumb_code, breadcrumb_name, is_deleted, create_time, last_modified_time, data_type, status " +
      "FROM mt_country_area_info WHERE tenant_id = #{tenantId} AND id > #{fromInfoId} ORDER BY id LIMIT #{limit}")
  List<MtCountryAreaInfo> getCountryInfoByTenantId(@Param("tenantId") String tenantId,
                                                   @Param("fromInfoId") String fromInfoId,
                                                   @Param("limit") Integer limit);

  @Select(
    "SELECT id, fs_unique_code, name, type, tenant_id, parent_fs_unique_code, latitude, longitude, breadcrumb_code, breadcrumb_name, is_deleted, create_time, last_modified_time, data_type, status " +
    "FROM mt_country_area_info WHERE tenant_id = #{tenantId} AND id = ANY(ARRAY[#{ids}])")
  List<MtCountryAreaInfo> getCountryInfosByIdArray(@Param("tenantId") String tenantId, @Param("ids") String[] ids);

  @Select("SELECT count(*) AS rows FROM mt_country_area_info WHERE tenant_id = #{tenantId} AND breadcrumb_name IS NOT NULL")
  Long getTenantMtAreaRows(@Param("tenantId") String tenantId);
}
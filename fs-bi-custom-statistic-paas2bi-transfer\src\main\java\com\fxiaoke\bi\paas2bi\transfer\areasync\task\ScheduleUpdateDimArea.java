package com.fxiaoke.bi.paas2bi.transfer.areasync.task;

import com.fxiaoke.bi.paas2bi.transfer.areasync.service.AreaInfoIncSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/11/28
 */
@Slf4j
@Service
@Lazy(value = false)
public class ScheduleUpdateDimArea {

  @Resource(name = "myExecutor")
  private ThreadPoolTaskExecutor myExecutor;
  @Resource
  private AreaInfoIncSyncService areaInfoIncSyncService;

  @Scheduled(cron = "0 0 1 * * ?")
  public void syncDimArea() {
    log.info("start etlDimArea job!");
    myExecutor.submit(areaInfoIncSyncService::syncDimAreaTiming);
  }


  @PreDestroy
  public void shutdown() {
    myExecutor.shutdown();
  }
}

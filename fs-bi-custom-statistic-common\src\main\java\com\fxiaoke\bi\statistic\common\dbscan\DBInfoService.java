package com.fxiaoke.bi.statistic.common.dbscan;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.regex.Pattern;

/**
 * Created by jief on 2020/8/5.
 */
@Setter
@Slf4j
public class DBInfoService {

  private OkHttpSupport okHttpSupport;

  /**
   * 数据库名前缀
   */
  private static final Pattern BI_SYSTEM_REGEX = Pattern.compile("^bi_system.*");
  private static final Pattern BI_DB_REGEX = Pattern.compile("^fsbidb.*");

  /**
   * 获取数据库名，按照ip分组
   *
   * @param schemaUrl 数据库ip地址
   * @param dialect   postgreSql
   * @param biz       BI
   * @return key: ip，value: 数据库名List
   */
  public Map<String, List<String>> getDBInfoFromRemote(String schemaUrl, String dialect, String biz) {
    HashMap<String, String> headers = new HashMap<>();
    String traceId = TraceContext.get().getTraceId();
    headers.put("X-fs-Trace-Id", traceId == null ? "" : traceId);
    headers.put("Content-Type", "application/json; charset=UTF-8");
    JSONObject jsonObject;
    try {
      jsonObject = this.getDBInfoFromRemote(String.format(schemaUrl, dialect, biz), headers);
      return this.parseJSON2Map(jsonObject);
    } catch (Exception e) {
      log.error("get db info map from remote url:{}", schemaUrl, e);
    }
    return null;
  }

  public List<DBInfo> getDBInfoListFromRemote(String schemaUrl, String dialect, String biz) {
    HashMap<String, String> headers = new HashMap<>();
    String traceId = TraceContext.get().getTraceId();
    headers.put("X-fs-Trace-Id", traceId == null ? "" : traceId);
    headers.put("Content-Type", "application/json; charset=UTF-8");
    JSONObject jsonObject;
    try {
      jsonObject = this.getDBInfoFromRemote(String.format(schemaUrl, dialect, biz), headers);
      return this.parseJSON2List(jsonObject);
    } catch (Exception e) {
      log.error("get db info map from remote url:{}", schemaUrl, e);
    }
    return null;
  }

  private JSONObject getDBInfoFromRemote(String url, Map<String, String> headers) {
    log.info("http get url:{},header:{}", url, headers);
    Request.Builder builder = new Request.Builder();
    if (MapUtils.isNotEmpty(headers)) {
      headers.forEach(builder::addHeader);
    }
    Request request = builder.url(url).build();
    return (JSONObject) okHttpSupport.syncExecute(request, new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        if (Objects.isNull(response.body())) {
          throw new RuntimeException("response is null");
        }
        String res = response.body().string();
        if (response.isSuccessful()) {
          return JSON.parseObject(res);
        } else {
          throw new RuntimeException("response error:" + res);
        }
      }
    });
  }

  /**
   * 返回打散后的ip地址和库名
   */
  private List<DBInfo> parseJSON2List(JSONObject jsonObject) {
    JSONArray jsonArray = jsonObject.getJSONArray("data");
    if (jsonArray == null || jsonArray.isEmpty()) {
      return null;
    }
    HashSet<DBInfo> ipDbSet = new HashSet<>();
    if ("fstest".equalsIgnoreCase(ConfigHelper.getProcessInfo().getProfile())) {
      jsonArray.stream()
               .map(obj -> this.parse2DBInfo((JSONObject) obj))
               .filter(dbInfo -> !BI_SYSTEM_REGEX.matcher(dbInfo.getDbName()).matches())
               .forEach(ipDbSet::add);
    } else {
      jsonArray.stream()
               .map(obj -> this.parse2DBInfo((JSONObject) obj))
               .filter(dbInfo -> BI_DB_REGEX.matcher(dbInfo.getDbName()).matches())
               .forEach(ipDbSet::add);
    }
    return Lists.newArrayList(ipDbSet);
  }

  /**
   * 获取ip和db映射关系
   */
  private Map<String, List<String>> parseJSON2Map(JSONObject jsonObject) {
    JSONArray jsonArray = jsonObject.getJSONArray("data");
    if (jsonArray == null || jsonArray.isEmpty()) {
      return null;
    }
    Map<String, List<String>> ipDbMap = Maps.newHashMap();
    if ("fstest".equalsIgnoreCase(ConfigHelper.getProcessInfo().getProfile())) {
      jsonArray.stream()
               .map(obj -> this.parse2DBInfo((JSONObject) obj))
               .filter(dbInfo -> !BI_SYSTEM_REGEX.matcher(dbInfo.getDbName()).matches())
               .forEach(dbInfo -> {
                 List<String> dbs = ipDbMap.getOrDefault(dbInfo.getIp(), Lists.newArrayList());
                 dbs.add(dbInfo.getDbName());
                 ipDbMap.putIfAbsent(dbInfo.getIp(), dbs);
               });
    } else {
      jsonArray.stream()
               .map(obj -> this.parse2DBInfo((JSONObject) obj))
               .filter(dbInfo -> BI_DB_REGEX.matcher(dbInfo.getDbName()).matches())
               .forEach(dbInfo -> {
                 List<String> dbs = ipDbMap.getOrDefault(dbInfo.getIp(), Lists.newArrayList());
                 dbs.add(dbInfo.getDbName());
                 ipDbMap.putIfAbsent(dbInfo.getIp(), dbs);
               });
    }
    return ipDbMap;
  }

  /**
   * 获取ip和dbame
   */
  private DBInfo parse2DBInfo(JSONObject jsonObject) {
    DBInfo dbInfo = new DBInfo();
    dbInfo.setDbName(jsonObject.getString("dbName"));
    dbInfo.setIp(jsonObject.getString("master"));
    dbInfo.setIpProxy(jsonObject.getString("masterProxyUrl"));
    return dbInfo;
  }

}

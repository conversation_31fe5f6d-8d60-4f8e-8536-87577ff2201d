package com.fxiaoke.bi.paas2bi.transfer.mapper.bi;

import com.fxiaoke.bi.paas2bi.transfer.pojo.DimSysDate;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DimSysDateMapper extends ITenant<DimSysDateMapper> {

  //查询财年每个周的周一日期
  @Select("select a.* from ( " +
    "                  select *,row_number() over (partition by f_week order by pk asc) as rank " +
    "                  from dim_sys_date where f_year between #{fromYear} and #{toYear} " +
    "                    and f_week >= #{fromWeek}  and f_week < #{toWeek} " +
    "                    and ei = -1  ) as a  where a.rank=1 ")
  List<DimSysDate> findAllMonDayByFiscalYear(@Param("fromYear") Integer fromYear,
                                             @Param("toYear") Integer toYear,
                                             @Param("fromWeek") String fromWeek,
                                             @Param("toWeek") String toWeek);

  //查询财年每个月的起始天
  @Select("select a.* from ( " +
    "      select *,row_number() over (partition by f_month order by pk asc) as rank " +
    "      from dim_sys_date where " +
    "      f_month >= cast(#{fromMonth} as integer) and f_month < cast(#{toMonth} as integer) and ei = -1 ) as a  where a.rank=1 ")
  List<DimSysDate> findAllMonthByFiscalYear(@Param("fromMonth") String fromMonth, @Param("toMonth") String toMonth);

  //查询财年每个季度的起始天
  @Select("select a.* from ( " +
    "                  select *,row_number() over (partition by f_season order by pk asc) as rank " +
    "                  from dim_sys_date where " +
    "                  f_season >= #{fromQuarter} and f_season < #{toQuarter} " +
    "                  and ei = -1 ) as a  where a.rank=1 ")
  List<DimSysDate> findAllQuarterByFiscalYear(@Param("fromQuarter") String fromQuarter,
                                              @Param("toQuarter") String toQuarter);

  //查找某一天日期信息
  @Select("select pk,f_year,f_month,f_day,f_season,f_week from dim_sys_date where pk=#{pk} and ei = -1")
  DimSysDate findDayOfYear(@Param("pk") String pk);
}

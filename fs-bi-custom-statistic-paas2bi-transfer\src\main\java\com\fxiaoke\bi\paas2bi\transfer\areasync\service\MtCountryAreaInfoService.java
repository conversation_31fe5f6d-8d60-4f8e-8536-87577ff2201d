package com.fxiaoke.bi.paas2bi.transfer.areasync.service;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.paas2bi.transfer.areasync.bean.DimSysArea;
import com.fxiaoke.bi.paas2bi.transfer.areasync.bean.DimSysAreaCount;
import com.fxiaoke.bi.paas2bi.transfer.areasync.bean.MtCountryAreaInfo;
import com.fxiaoke.bi.paas2bi.transfer.mapper.paas.MtCountryAreaInfoMapper;
import com.fxiaoke.bi.paas2bi.transfer.areasync.utils.AreaCommonUtils;
import com.fxiaoke.bi.paas2bi.transfer.areasync.utils.MultiLangProcessor;
import com.fxiaoke.bi.paas2bi.transfer.bean.PageInfo;
import com.fxiaoke.bi.paas2bi.transfer.service.BIPGDataSource;
import com.fxiaoke.bi.paas2bi.transfer.service.PaasPgDataSource;
import com.fxiaoke.common.Pair;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.fxiaoke.bi.paas2bi.transfer.areasync.utils.AreaCommonUtils.*;

/**
 * 使用Jdbc查询 paas.metadata_sys 系统库的 mt_country_area_info
 *
 * <AUTHOR> Zhenghao
 * @date 2022/8/11
 */
@Slf4j
@Service
public class MtCountryAreaInfoService {

  @Resource
  private PaasPgDataSource paasPgDataSource;
  @Resource
  private BIPGDataSource bipgDataSource;
  @Resource
  private MtCountryAreaInfoMapper mtCountryAreaInfoMapper;
  @Resource
  private MultiLangProcessor multiLangProcessor;

  /**
   * 根据 tenant_id 查询 mt_country_area_info
   */
  public static final String SELECT_MT_AREA_ALIAS =
    "SELECT id, fs_unique_code AS \"fsUniqueCode\", name, type, tenant_id AS \"tenantId\", " +
    "parent_fs_unique_code AS \"parentFsUniqueCode\", latitude, longitude, " +
    "breadcrumb_code AS \"breadcrumbCode\", breadcrumb_name AS \"breadcrumbName\", " +
    "lang_1 AS \"lang1\", lang_2 AS \"lang2\", lang_3 AS \"lang3\", lang_4 AS \"lang4\", " +
    "lang_5 AS \"lang5\", lang_6 AS \"lang6\", lang_7 AS \"lang7\", lang_8 AS \"lang8\", " +
    "lang_9 AS \"lang9\", lang_10 AS \"lang10\", lang_11 AS \"lang11\", lang_12 AS \"lang12\", " +
    "lang_13 AS \"lang13\", lang_14 AS \"lang14\", lang_15 AS \"lang15\", " +
    "lang_16 AS \"lang16\", lang_17 AS \"lang17\", lang_18 AS \"lang18\", lang_19 AS \"lang19\", " +
    "lang_20 AS \"lang20\", lang_21 AS \"lang21\", lang_22 AS \"lang22\", " +
    "lang_23 AS \"lang23\", lang_24 AS \"lang24\", " +
    "breadcrumb_name_lang1 AS \"breadcrumbNameLang1\", breadcrumb_name_lang2 AS \"breadcrumbNameLang2\", " +
    "breadcrumb_name_lang3 AS \"breadcrumbNameLang3\", breadcrumb_name_lang4 AS \"breadcrumbNameLang4\", " +
    "breadcrumb_name_lang5 AS \"breadcrumbNameLang5\", breadcrumb_name_lang6 AS \"breadcrumbNameLang6\", " +
    "breadcrumb_name_lang7 AS \"breadcrumbNameLang7\", breadcrumb_name_lang8 AS \"breadcrumbNameLang8\", " +
    "breadcrumb_name_lang9 AS \"breadcrumbNameLang9\", breadcrumb_name_lang10 AS \"breadcrumbNameLang10\", " +
    "breadcrumb_name_lang11 AS \"breadcrumbNameLang11\", breadcrumb_name_lang12 AS \"breadcrumbNameLang12\", " +
    "breadcrumb_name_lang13 AS \"breadcrumbNameLang13\", breadcrumb_name_lang14 AS \"breadcrumbNameLang14\", " +
    "breadcrumb_name_lang15 AS \"breadcrumbNameLang15\", " +
    "breadcrumb_name_lang16 AS \"breadcrumbNameLang16\", breadcrumb_name_lang17 AS \"breadcrumbNameLang17\", " +
    "breadcrumb_name_lang18 AS \"breadcrumbNameLang18\", breadcrumb_name_lang19 AS \"breadcrumbNameLang19\", " +
    "breadcrumb_name_lang20 AS \"breadcrumbNameLang20\", breadcrumb_name_lang21 AS \"breadcrumbNameLang21\", " +
    "breadcrumb_name_lang22 AS \"breadcrumbNameLang22\", " +
    "breadcrumb_name_lang23 AS \"breadcrumbNameLang23\", breadcrumb_name_lang24 AS \"breadcrumbNameLang24\", " +
    "type, is_deleted \"isDeleted\", create_time \"createTime\", " +
    "last_modified_time \"lastModifiedTime\", data_type \"dataType\", status " +
    "FROM %s WHERE tenant_id = '-100' AND id > ? ORDER BY id LIMIT ?";

  /**
   * 根据 code 查询 mt_country_area_info
   */
  public static final String SELECT_MT_AREA_ALIAS_CODE =
    "SELECT fs_unique_code AS \"fsUniqueCode\", NULLIF(name, '') AS \"name\", fs_unique_code_int \"fsUniqueCodeInt\", type, " +
    "lang_1 AS \"lang1\", lang_2 AS \"lang2\", lang_3 AS \"lang3\", lang_4 AS \"lang4\", lang_5 AS \"lang5\", " +
    "lang_6 AS \"lang6\", lang_7 AS \"lang7\", lang_8 AS \"lang8\", lang_9 AS \"lang9\", lang_10 AS \"lang10\", " +
    "lang_11 AS \"lang11\", lang_12 AS \"lang12\", lang_13 AS \"lang13\", lang_14 AS \"lang14\", lang_15 AS \"lang15\", " +
    "lang_16 AS \"lang16\", lang_17 AS \"lang17\", lang_18 AS \"lang18\", lang_19 AS \"lang19\", " +
    "lang_20 AS \"lang20\", lang_21 AS \"lang21\", lang_22 AS \"lang22\", " +
    "lang_23 AS \"lang23\", lang_24 AS \"lang24\" " +
    "FROM %s WHERE tenant_id = '-100' AND fs_unique_code_int = ANY(array%s) ORDER BY fs_unique_code_int";

  /**
   * 根据 id 查询 mt_country_area_info
   */
  public static final String SELECT_MT_AREA_ALIAS_ID =
    "SELECT id, fs_unique_code AS \"fsUniqueCode\", name, type, tenant_id AS \"tenantId\", " +
    "parent_fs_unique_code AS \"parentFsUniqueCode\", latitude, longitude, " +
    "breadcrumb_code AS \"breadcrumbCode\", breadcrumb_name AS \"breadcrumbName\", " +
    "lang_1 AS \"lang1\", lang_2 AS \"lang2\", lang_3 AS \"lang3\", lang_4 AS \"lang4\", " +
    "lang_5 AS \"lang5\", lang_6 AS \"lang6\", lang_7 AS \"lang7\", lang_8 AS \"lang8\", " +
    "lang_9 AS \"lang9\", lang_10 AS \"lang10\", lang_11 AS \"lang11\", lang_12 AS \"lang12\", " +
    "lang_13 AS \"lang13\", lang_14 AS \"lang14\", lang_15 AS \"lang15\", " +
    "lang_16 AS \"lang16\", lang_17 AS \"lang17\", lang_18 AS \"lang18\", lang_19 AS \"lang19\", " +
    "lang_20 AS \"lang20\", lang_21 AS \"lang21\", lang_22 AS \"lang22\", " +
    "lang_23 AS \"lang23\", lang_24 AS \"lang24\", " +
    "breadcrumb_name_lang1 AS \"breadcrumbNameLang1\", breadcrumb_name_lang2 AS \"breadcrumbNameLang2\", " +
    "breadcrumb_name_lang3 AS \"breadcrumbNameLang3\", breadcrumb_name_lang4 AS \"breadcrumbNameLang4\", " +
    "breadcrumb_name_lang5 AS \"breadcrumbNameLang5\", breadcrumb_name_lang6 AS \"breadcrumbNameLang6\", " +
    "breadcrumb_name_lang7 AS \"breadcrumbNameLang7\", breadcrumb_name_lang8 AS \"breadcrumbNameLang8\", " +
    "breadcrumb_name_lang9 AS \"breadcrumbNameLang9\", breadcrumb_name_lang10 AS \"breadcrumbNameLang10\", " +
    "breadcrumb_name_lang11 AS \"breadcrumbNameLang11\", breadcrumb_name_lang12 AS \"breadcrumbNameLang12\", " +
    "breadcrumb_name_lang13 AS \"breadcrumbNameLang13\", breadcrumb_name_lang14 AS \"breadcrumbNameLang14\", " +
    "breadcrumb_name_lang15 AS \"breadcrumbNameLang15\", " +
    "breadcrumb_name_lang16 AS \"breadcrumbNameLang16\", breadcrumb_name_lang17 AS \"breadcrumbNameLang17\", " +
    "breadcrumb_name_lang18 AS \"breadcrumbNameLang18\", breadcrumb_name_lang19 AS \"breadcrumbNameLang19\", " +
    "breadcrumb_name_lang20 AS \"breadcrumbNameLang20\", breadcrumb_name_lang21 AS \"breadcrumbNameLang21\", " +
    "breadcrumb_name_lang22 AS \"breadcrumbNameLang22\", " +
    "breadcrumb_name_lang23 AS \"breadcrumbNameLang23\", breadcrumb_name_lang24 AS \"breadcrumbNameLang24\", " +
    "type, is_deleted \"isDeleted\", create_time \"createTime\", " +
    "last_modified_time \"lastModifiedTime\", data_type \"dataType\", status " +
    "FROM %s WHERE tenant_id = '-100' AND id = ANY(array%s) ORDER BY id";

  /**
   * 查询指定更新时间内的数据
   */
  public static final String SELECT_MT_AREA_ALIAS_TIME_INTERVAL =
    "SELECT id, fs_unique_code AS \"fsUniqueCode\", name, type, tenant_id AS \"tenantId\", " +
    "parent_fs_unique_code AS \"parentFsUniqueCode\", latitude, longitude, " +
    "breadcrumb_code AS \"breadcrumbCode\", breadcrumb_name AS \"breadcrumbName\", " +
    "lang_1 AS \"lang1\", lang_2 AS \"lang2\", lang_3 AS \"lang3\", lang_4 AS \"lang4\", " +
    "lang_5 AS \"lang5\", lang_6 AS \"lang6\", lang_7 AS \"lang7\", lang_8 AS \"lang8\", " +
    "lang_9 AS \"lang9\", lang_10 AS \"lang10\", lang_11 AS \"lang11\", lang_12 AS \"lang12\", " +
    "lang_13 AS \"lang13\", lang_14 AS \"lang14\", lang_15 AS \"lang15\", " +
    "lang_16 AS \"lang16\", lang_17 AS \"lang17\", lang_18 AS \"lang18\", lang_19 AS \"lang19\", " +
    "lang_20 AS \"lang20\", lang_21 AS \"lang21\", lang_22 AS \"lang22\", " +
    "lang_23 AS \"lang23\", lang_24 AS \"lang24\", " +
    "breadcrumb_name_lang1 AS \"breadcrumbNameLang1\", breadcrumb_name_lang2 AS \"breadcrumbNameLang2\", " +
    "breadcrumb_name_lang3 AS \"breadcrumbNameLang3\", breadcrumb_name_lang4 AS \"breadcrumbNameLang4\", " +
    "breadcrumb_name_lang5 AS \"breadcrumbNameLang5\", breadcrumb_name_lang6 AS \"breadcrumbNameLang6\", " +
    "breadcrumb_name_lang7 AS \"breadcrumbNameLang7\", breadcrumb_name_lang8 AS \"breadcrumbNameLang8\", " +
    "breadcrumb_name_lang9 AS \"breadcrumbNameLang9\", breadcrumb_name_lang10 AS \"breadcrumbNameLang10\", " +
    "breadcrumb_name_lang11 AS \"breadcrumbNameLang11\", breadcrumb_name_lang12 AS \"breadcrumbNameLang12\", " +
    "breadcrumb_name_lang13 AS \"breadcrumbNameLang13\", breadcrumb_name_lang14 AS \"breadcrumbNameLang14\", " +
    "breadcrumb_name_lang15 AS \"breadcrumbNameLang15\", " +
    "breadcrumb_name_lang16 AS \"breadcrumbNameLang16\", breadcrumb_name_lang17 AS \"breadcrumbNameLang17\", " +
    "breadcrumb_name_lang18 AS \"breadcrumbNameLang18\", breadcrumb_name_lang19 AS \"breadcrumbNameLang19\", " +
    "breadcrumb_name_lang20 AS \"breadcrumbNameLang20\", breadcrumb_name_lang21 AS \"breadcrumbNameLang21\", " +
    "breadcrumb_name_lang22 AS \"breadcrumbNameLang22\", " +
    "breadcrumb_name_lang23 AS \"breadcrumbNameLang23\", breadcrumb_name_lang24 AS \"breadcrumbNameLang24\", " +
    "type, is_deleted \"isDeleted\", create_time \"createTime\", " +
    "last_modified_time \"lastModifiedTime\", data_type \"dataType\", status " +
    "FROM %s WHERE tenant_id = '-100' AND id > ? AND last_modified_time >= ? AND last_modified_time <= ? ORDER BY id LIMIT ?";

  /**
   * 查询更新时间大于指定时间的数据
   */
  public static final String SELECT_MT_AREA_ALIAS_TIME =
    "SELECT id, fs_unique_code AS \"fsUniqueCode\", name, type, tenant_id AS \"tenantId\", " +
    "parent_fs_unique_code AS \"parentFsUniqueCode\", latitude, longitude, " +
    "breadcrumb_code AS \"breadcrumbCode\", breadcrumb_name AS \"breadcrumbName\", " +
    "lang_1 AS \"lang1\", lang_2 AS \"lang2\", lang_3 AS \"lang3\", lang_4 AS \"lang4\", " +
    "lang_5 AS \"lang5\", lang_6 AS \"lang6\", lang_7 AS \"lang7\", lang_8 AS \"lang8\", " +
    "lang_9 AS \"lang9\", lang_10 AS \"lang10\", lang_11 AS \"lang11\", lang_12 AS \"lang12\", " +
    "lang_13 AS \"lang13\", lang_14 AS \"lang14\", lang_15 AS \"lang15\", " +
    "lang_16 AS \"lang16\", lang_17 AS \"lang17\", lang_18 AS \"lang18\", lang_19 AS \"lang19\", " +
    "lang_20 AS \"lang20\", lang_21 AS \"lang21\", lang_22 AS \"lang22\", " +
    "lang_23 AS \"lang23\", lang_24 AS \"lang24\", " +
    "breadcrumb_name_lang1 AS \"breadcrumbNameLang1\", breadcrumb_name_lang2 AS \"breadcrumbNameLang2\", " +
    "breadcrumb_name_lang3 AS \"breadcrumbNameLang3\", breadcrumb_name_lang4 AS \"breadcrumbNameLang4\", " +
    "breadcrumb_name_lang5 AS \"breadcrumbNameLang5\", breadcrumb_name_lang6 AS \"breadcrumbNameLang6\", " +
    "breadcrumb_name_lang7 AS \"breadcrumbNameLang7\", breadcrumb_name_lang8 AS \"breadcrumbNameLang8\", " +
    "breadcrumb_name_lang9 AS \"breadcrumbNameLang9\", breadcrumb_name_lang10 AS \"breadcrumbNameLang10\", " +
    "breadcrumb_name_lang11 AS \"breadcrumbNameLang11\", breadcrumb_name_lang12 AS \"breadcrumbNameLang12\", " +
    "breadcrumb_name_lang13 AS \"breadcrumbNameLang13\", breadcrumb_name_lang14 AS \"breadcrumbNameLang14\", " +
    "breadcrumb_name_lang15 AS \"breadcrumbNameLang15\", " +
    "breadcrumb_name_lang16 AS \"breadcrumbNameLang16\", breadcrumb_name_lang17 AS \"breadcrumbNameLang17\", " +
    "breadcrumb_name_lang18 AS \"breadcrumbNameLang18\", breadcrumb_name_lang19 AS \"breadcrumbNameLang19\", " +
    "breadcrumb_name_lang20 AS \"breadcrumbNameLang20\", breadcrumb_name_lang21 AS \"breadcrumbNameLang21\", " +
    "breadcrumb_name_lang22 AS \"breadcrumbNameLang22\", " +
    "breadcrumb_name_lang23 AS \"breadcrumbNameLang23\", breadcrumb_name_lang24 AS \"breadcrumbNameLang24\", " +
    "type, is_deleted \"isDeleted\", create_time \"createTime\", " +
    "last_modified_time \"lastModifiedTime\", data_type \"dataType\", status " +
    "FROM %s WHERE tenant_id = '-100' AND id > ? AND last_modified_time >= ? ORDER BY id LIMIT ?";

  /**
   * 查询租户自定义省市区数据
   */
  public static final String SELECT_MT_AREA_ALIAS_TENANT =
    "SELECT id, fs_unique_code AS \"fsUniqueCode\", name, type, tenant_id AS \"tenantId\", " +
    "parent_fs_unique_code AS \"parentFsUniqueCode\", latitude, longitude, " +
    "breadcrumb_code AS \"breadcrumbCode\", breadcrumb_name AS \"breadcrumbName\", " +
    "lang_1 AS \"lang1\", lang_2 AS \"lang2\", lang_3 AS \"lang3\", lang_4 AS \"lang4\", " +
    "lang_5 AS \"lang5\", lang_6 AS \"lang6\", lang_7 AS \"lang7\", lang_8 AS \"lang8\", " +
    "lang_9 AS \"lang9\", lang_10 AS \"lang10\", lang_11 AS \"lang11\", lang_12 AS \"lang12\", " +
    "lang_13 AS \"lang13\", lang_14 AS \"lang14\", lang_15 AS \"lang15\", " +
    "lang_16 AS \"lang16\", lang_17 AS \"lang17\", lang_18 AS \"lang18\", lang_19 AS \"lang19\", " +
    "lang_20 AS \"lang20\", lang_21 AS \"lang21\", lang_22 AS \"lang22\", " +
    "lang_23 AS \"lang23\", lang_24 AS \"lang24\", " +
    "breadcrumb_name_lang1 AS \"breadcrumbNameLang1\", breadcrumb_name_lang2 AS \"breadcrumbNameLang2\", " +
    "breadcrumb_name_lang3 AS \"breadcrumbNameLang3\", breadcrumb_name_lang4 AS \"breadcrumbNameLang4\", " +
    "breadcrumb_name_lang5 AS \"breadcrumbNameLang5\", breadcrumb_name_lang6 AS \"breadcrumbNameLang6\", " +
    "breadcrumb_name_lang7 AS \"breadcrumbNameLang7\", breadcrumb_name_lang8 AS \"breadcrumbNameLang8\", " +
    "breadcrumb_name_lang9 AS \"breadcrumbNameLang9\", breadcrumb_name_lang10 AS \"breadcrumbNameLang10\", " +
    "breadcrumb_name_lang11 AS \"breadcrumbNameLang11\", breadcrumb_name_lang12 AS \"breadcrumbNameLang12\", " +
    "breadcrumb_name_lang13 AS \"breadcrumbNameLang13\", breadcrumb_name_lang14 AS \"breadcrumbNameLang14\", " +
    "breadcrumb_name_lang15 AS \"breadcrumbNameLang15\", " +
    "breadcrumb_name_lang16 AS \"breadcrumbNameLang16\", breadcrumb_name_lang17 AS \"breadcrumbNameLang17\", " +
    "breadcrumb_name_lang18 AS \"breadcrumbNameLang18\", breadcrumb_name_lang19 AS \"breadcrumbNameLang19\", " +
    "breadcrumb_name_lang20 AS \"breadcrumbNameLang20\", breadcrumb_name_lang21 AS \"breadcrumbNameLang21\", " +
    "breadcrumb_name_lang22 AS \"breadcrumbNameLang22\", " +
    "breadcrumb_name_lang23 AS \"breadcrumbNameLang23\", breadcrumb_name_lang24 AS \"breadcrumbNameLang24\", " +
    "type, is_deleted \"isDeleted\", create_time \"createTime\", " +
    "last_modified_time \"lastModifiedTime\", data_type \"dataType\", status " +
    "FROM %s WHERE id > ? ORDER BY id LIMIT ?";

  /**
   * 查询 paas 系统库国家省市区数据条数
   */
  public static final String SELECT_MT_AREA_COUNT = "SELECT COUNT(*) AS rows FROM %s WHERE tenant_id = '-100' AND breadcrumb_name IS NOT NULL AND (parent_fs_unique_code IS NOT NULL OR type = '国家')";

  /**
   * 地区最长的级别
   * 全部/中国/黑龙江省/哈尔滨市/双城区/新兴街道/中山区  7级 格式错误
   * 全部/中国/黑龙江省/哈尔滨市/双城区/新兴街道  6级 格式正确
   */
  public static final int MAX_AREA_LEVEL = 6;

  /**
   * 分页获取paas系统库的mt_country_area_info数据
   *
   * @param id       当前页最后一条
   * @param pageSize 页面大小
   */
  public List<MtCountryAreaInfo> getMtAreaPaasSys(String id, Integer pageSize) {
    JdbcConnection jdbcConnection = paasPgDataSource.getJdbcConnectionByTenantId(PAAS_SYS_TENANT);
    String areaTable = usePaasAreaTranslate == 1 ? MT_COUNTRY_AREA_INFO_TRANSLATE : MT_COUNTRY_AREA_INFO;
    return AreaCommonUtils.queryMany(String.format(SELECT_MT_AREA_ALIAS, areaTable), jdbcConnection, MtCountryAreaInfo.class, id, pageSize);
  }

  /**
   * 根据code查询数据
   *
   * @param codes 数据唯一编码数组
   */
  public List<MtCountryAreaInfo> getMtAreaPaasSysByCodes(String[] codes) {
    ArrayList<String> codeList = Lists.newArrayList(codes);
    codeList.remove(0);
    String[] codeNoRoot = codeList.toArray(new String[0]);
    JdbcConnection jdbcConnection = paasPgDataSource.getJdbcConnectionByTenantId(PAAS_SYS_TENANT);
    String areaTable = usePaasAreaTranslate == 1 ? MT_COUNTRY_AREA_INFO_TRANSLATE : MT_COUNTRY_AREA_INFO;
    String sql = String.format(SELECT_MT_AREA_ALIAS_CODE, areaTable, Arrays.toString(codeNoRoot));
    return AreaCommonUtils.queryMany(sql, jdbcConnection, MtCountryAreaInfo.class);
  }

  /**
   * 根据code查询数据
   *
   * @param ids 数据唯一编码数组
   */
  public List<MtCountryAreaInfo> getMtAreaPaasSysByIds(String[] ids) {
    JdbcConnection jdbcConnection = paasPgDataSource.getJdbcConnectionByTenantId(PAAS_SYS_TENANT);
    String[] idStrs = Arrays.stream(ids).map(id -> String.format("'%s'", id)).toArray(String[]::new);
    String areaTable = usePaasAreaTranslate == 1 ? MT_COUNTRY_AREA_INFO_TRANSLATE : MT_COUNTRY_AREA_INFO;
    String sql = String.format(SELECT_MT_AREA_ALIAS_ID, areaTable, Arrays.toString(idStrs));
    return AreaCommonUtils.queryMany(sql, jdbcConnection, MtCountryAreaInfo.class);
  }

  /**
   * 查询更新时间大于在指定时间范围内的数据
   *
   * @param timeInterval first: 开始时间戳, second: 结束时间戳
   */
  public List<MtCountryAreaInfo> getMtAreaPaasSys(String id, Pair<Long, Long> timeInterval, Integer pageSize) {
    JdbcConnection jdbcConnection = paasPgDataSource.getJdbcConnectionByTenantId(PAAS_SYS_TENANT);
    String areaTable = usePaasAreaTranslate == 1 ? MT_COUNTRY_AREA_INFO_TRANSLATE : MT_COUNTRY_AREA_INFO;
    return AreaCommonUtils.queryMany(String.format(SELECT_MT_AREA_ALIAS_TIME_INTERVAL, areaTable), jdbcConnection, MtCountryAreaInfo.class, id, timeInterval.first, timeInterval.second, pageSize);
  }

  /**
   * 查询更新时间大于指定时间的数据
   *
   * @param timeStart last_modified_time 开始时间戳
   */
  public List<MtCountryAreaInfo> getMtAreaPaasSys(String id, Long timeStart, Integer pageSize) {
    JdbcConnection jdbcConnection = paasPgDataSource.getJdbcConnectionByTenantId(PAAS_SYS_TENANT);
    String areaTable = usePaasAreaTranslate == 1 ? MT_COUNTRY_AREA_INFO_TRANSLATE : MT_COUNTRY_AREA_INFO;
    return AreaCommonUtils.queryMany(String.format(SELECT_MT_AREA_ALIAS_TIME, areaTable), jdbcConnection, MtCountryAreaInfo.class, id, timeStart, pageSize);
  }

  /**
   * 查询 bi 租户库的 mt_country_area_info
   */
  public List<MtCountryAreaInfo> getMtAreaDimPublic(String tenantId, String lastFlag) {
    return mtCountryAreaInfoMapper.setTenantId(tenantId).getCountryInfoByTenantId(tenantId, lastFlag, PAGE_SIZE);
  }

  /**
   * 通过 id 数组查询 bi 租户库的 mt_country_area_info
   */
  public List<MtCountryAreaInfo> getMtAreaDimPublic(String tenantId, String[] idArray) {
    return mtCountryAreaInfoMapper.setTenantId(tenantId).getCountryInfosByIdArray(tenantId, idArray);
  }

  /**
   * 查询 paas 系统库 mt_country_area_info 国家省市区数据条数
   *
   * @return 数据行数
   */
  public Long getPaasSysMtAreaRows() {
    JdbcConnection jdbcConnection = paasPgDataSource.getJdbcConnectionByTenantId(PAAS_SYS_TENANT);
    String areaTable = usePaasAreaTranslate == 1 ? MT_COUNTRY_AREA_INFO_TRANSLATE : MT_COUNTRY_AREA_INFO;
    DimSysAreaCount count = queryOne(String.format(SELECT_MT_AREA_COUNT, areaTable), jdbcConnection, DimSysAreaCount.class);
    return Objects.isNull(count) ? 0 : count.getRows();
  }

  /**
   * 查询 bi 租户库 mt_country_area_info 国家省市区数据条数
   *
   * @return 数据行数
   */
  public Long getTenantMtAreaRows(String tenantId) {
    Long tenantMtAreaRows = mtCountryAreaInfoMapper.setTenantId(tenantId).getTenantMtAreaRows(tenantId);
    return Objects.isNull(tenantMtAreaRows) ? 0 : tenantMtAreaRows;
  }

  /**
   * 查询租户国家省市区数据
   */
  public List<MtCountryAreaInfo> getMtAreaPaasPublic(String dbUrl, int pageSize, String lastId) {
    JdbcConnection jdbcConnection = bipgDataSource.getJdbcConnection(dbUrl);
    String areaTable = usePaasAreaTranslate == 1 ? MT_COUNTRY_AREA_INFO_TRANSLATE : MT_COUNTRY_AREA_INFO;
    return AreaCommonUtils.queryMany(String.format(SELECT_MT_AREA_ALIAS_TENANT, areaTable), jdbcConnection, MtCountryAreaInfo.class, lastId, pageSize);
  }

  /**
   * 获取 dim_sys_area List
   */
  public List<DimSysArea> convertDimSysAreaList(String tenantId, final PageInfo<MtCountryAreaInfo> pageInfo) {
    List<MtCountryAreaInfo> countryAreaInfos = pageInfo.getQueryList();
    String tenantIdSys = PAAS_SYS_TENANT.equals(tenantId) ? BI_SYS_TENANT : tenantId;
    return countryAreaInfos.stream()
                           .filter(info -> StringUtils.isNotBlank(info.getBreadcrumbCode()) &&
                                           (StringUtils.isNotBlank(info.getParentFsUniqueCode()) ||
                                            "国家".equals(info.getType())))
                           .map(info -> this.buildDimSysArea(tenantIdSys, info))
                           .filter(Objects::nonNull)
                           .collect(Collectors.toList());
  }

  /**
   * 处理各级行政区
   *
   * @param info paas 地区数据
   * @return {@link DimSysArea}
   */
  public DimSysArea buildDimSysArea(String tenantId, MtCountryAreaInfo info) {
    if ("public".equals(tenantId)) {
      tenantId = info.getTenantId();
    }
    Map<String, Object> codeMap = new HashMap<>();
    String fsUniqueCode = info.getFsUniqueCode();
    String parentFsUniqueCode = info.getParentFsUniqueCode();
    String name = info.getName();
    String breadcrumbCode = info.getBreadcrumbCode();
    String breadcrumbName = info.getBreadcrumbName();
    String[] areaCodes = StringUtils.split(breadcrumbCode, SEPARATOR_CHAR);
    String[] areaNames = StringUtils.split(breadcrumbName, SEPARATOR_CHAR);
    int lengthCodes = areaCodes.length;
    int lengthNames = areaNames.length;
    boolean codeFalse = lengthCodes > MAX_AREA_LEVEL;
    // 编码数据格式不对则为脏数据
    if (codeFalse) {
      log.info("mt_country_area_info code error data id: {}, code: {}, name: {}", info.getId(), fsUniqueCode, name);
      return null;
    }
    // 名称中可能包含符号 '/' 导致名称分割数组出错，此时通过数据库反查出各级 name
    boolean nameFalse = lengthNames != lengthCodes;
    if (nameFalse) {
      if (nameErrorLogEnable) {
        log.error("mt_country_area_info name error data id: {}, code: {}, name: {}", info.getId(), fsUniqueCode, name);
        NAME_SPILT_ERROR.add(info.getId());
      }
      // 各级名称中有名称包含 '/' 反查数据库拼接各级地区名称数据
      List<MtCountryAreaInfo> mtCountryAreaInfos = getMtAreaPaasSysByCodes(areaCodes);
      // 补充根节点 全部 或 All
      MtCountryAreaInfo rootAreaInfo = new MtCountryAreaInfo();
      rootAreaInfo.setName(areaNames[0]);
      mtCountryAreaInfos.add(0, rootAreaInfo);
      // 存在 fs_unique_code 在数据库查不到的情况，此时查不到的行政区级别存为NULL
      if (mtCountryAreaInfos.size() != lengthCodes) {
        mtCountryAreaInfos.stream()
                          .filter(info1 -> Objects.nonNull(info1.getType()))
                          .forEach(areaInfo -> codeMap.put(areaInfo.convertDimFieldName(), areaInfo.getName()));
      } else {
        // 根据分割的 code 查出数据全的话就直接填充
        areaNames = mtCountryAreaInfos.stream().map(MtCountryAreaInfo::getName).toArray(String[]::new);
        for (int i = 1; i < lengthCodes; i++) {
          codeMap.put(DIM_AREA_NAMES[i], areaNames[i]);
        }
      }
    } else {
      // 名称不含 '/' 正常填充
      for (int i = 1; i < lengthCodes; i++) {
        codeMap.put(DIM_AREA_NAMES[i], areaNames[i]);
      }
    }
    for (int i = 1; i < lengthCodes; i++) {
      codeMap.put(DIM_AREA_CODES[i], areaCodes[i]);
    }
    String jsonMap = JSON.toJSONString(codeMap);
    DimSysArea dimSysArea = JSON.parseObject(jsonMap, DimSysArea.class);
    dimSysArea.setPk(fsUniqueCode);
    dimSysArea.setName(name);
    dimSysArea.setEi(Integer.parseInt(tenantId));
    dimSysArea.setLatitude(info.getLatitude());
    dimSysArea.setLongitude(info.getLongitude());
    dimSysArea.setParentPk(parentFsUniqueCode);
    String typeEn = areaTypeConvertEn(info.getType());
    dimSysArea.setType(typeEn);
    dimSysArea.setCountryAreaId(info.getId());
    dimSysArea.setIsDeleted(info.getIsDeleted());
    dimSysArea.setCreateTime(info.getCreateTime());
    dimSysArea.setLastModifiedTime(info.getLastModifiedTime());
    dimSysArea.setDataType(info.getDataType());
    dimSysArea.setStatus(info.getStatus());
    
    // 使用多语言处理器填充多语言字段
    multiLangProcessor.fillMultiLangFields(dimSysArea, info);
    
    return dimSysArea;
  }
}

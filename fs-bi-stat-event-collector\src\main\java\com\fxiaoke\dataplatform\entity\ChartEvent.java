package com.fxiaoke.dataplatform.entity;

import com.alibaba.fastjson.JSON;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChartEvent {

  @NonNull
  private String tenantId;
  @NonNull
  private String dataId;    //统计图、拼表、首页、驾驶舱、列表页 id
  @NonNull
  private String charType;  //图表:2;拼表:3;首页:4;列表页:5;驾驶舱:6;
  @NonNull
  private String kind;      //新建:I;更新:U;删除:D;
  private int retry = 0;

  public String toJSONString() {
    return JSON.toJSONString(this);
  }
}

package com.fxiaoke.bi.paas2bi.transfer.dispatcher.listener;

import com.fxiaoke.bi.paas2bi.transfer.bean.OpLog;
import com.fxiaoke.bi.paas2bi.transfer.service.DataCleanSynchronService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class ProductCategoryEventListener implements EventListener<OpLog> {

  @Resource
  private DataCleanSynchronService dataCleanSynchronService;

  @Override
  public void onTrigger(List<OpLog> events) throws Exception {
    dataCleanSynchronService.send2Gnomon(events);
  }
}

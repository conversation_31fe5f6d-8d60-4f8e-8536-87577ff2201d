package com.fxiaoke.bi.paas2bi.transfer.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.paas2bi.transfer.bean.OpLog;
import com.fxiaoke.bi.paas2bi.transfer.dao.GoalValueDao;
import com.fxiaoke.bi.paas2bi.transfer.dao.PaasGoalRuleDao;
import com.fxiaoke.bi.paas2bi.transfer.dispatcher.manger.DispatcherManager;
import com.fxiaoke.bi.paas2bi.transfer.handler.MultiDimGoalHandler;
import com.fxiaoke.bi.paas2bi.transfer.handler.NewGoalValueHandler;
import com.fxiaoke.bi.paas2bi.transfer.handler.NewGoalValueUpdateHandler;
import com.fxiaoke.bi.paas2bi.transfer.mapper.paas.GoalMapper;
import com.fxiaoke.bi.paas2bi.transfer.pojo.CheckDimensionField;
import com.fxiaoke.bi.paas2bi.transfer.pojo.GoalValuePlus;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.Uninterruptibles;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date: 2020/11/19
 * @Description
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext.xml")
public class GoalValueUpdateHandlerServiceTest {

  @Autowired
  GoalValueUpdateHandlerService goalValueUpdateHandlerService;
  @Resource
  private PaasGoalRuleDao paasGoalRuleDao;
  @Resource
  private GoalMapper goalMapper;
  @Resource
  private PaasDataParseListener paasDataParseListener;
  @Resource
  GoalValueDao goalValueDao;
  @Autowired
  NewGoalValueHandler newGoalValueHandler;
  @Autowired
  GoalRuleCacheService goalRuleCacheService;
  @Autowired
  MultiDimGoalHandler multiDimGoalHandler;
  @Autowired
  NewGoalValueUpdateHandler newGoalValueUpdateHandler;

  @Test
  public void testNewGoalUpdate() {
    String opLogStr = "{\"before\":{\"out_data_auth_id\":\"427905\",\"data_auth_code\":\"f872cfc3775c915d\",\"out_data_auth_code\":\"f175e5e1e8d8e6a0\",\"data_auth_id\":\"7338436\",\"sys_modified_time\":\"1742956870583282\"},\"user_id\":\"1000\",\"meta\":{\"schema\":\"public\",\"op\":\"U\",\"time\":\"2025-03-26 10:41:29.501066\",\"db\":\"bi_112\",\"table\":\"org_employee_user\"},\"keys\":{\"tenant_id\":\"86369\",\"id\":\"1000\"},\"object_describe_api_name\":\"PersonnelObj\",\"after\":{\"out_data_auth_id\":\"618769\",\"data_auth_code\":\"5d842a98ec7f942b\",\"out_data_auth_code\":\"c19389f4e9d0bf7c\",\"data_auth_id\":\"7775974\",\"sys_modified_time\":\"1742956889499616\"}}";
    OpLog opLog = JSON.parseObject(opLogStr, OpLog.class);
    newGoalValueUpdateHandler.batchHandler(Lists.newArrayList(opLog));
  }

  @Test
  public void testBatchEtlGoalRule() {
    goalValueDao.batchEtlGoalValueByTenantIds(Lists.newArrayList("86369"));
  }

  @Test
  public void testLogicDel() {
    goalValueDao.deleteGoalValue("85529", "65dc079b4e12590001efee7e");
  }

  @Test
  public void testGoalRuleCacheService() {
    List<CheckDimensionField> effectedGoalRuleList = goalRuleCacheService.getEffectedGoalRuleList("85529", Sets.newHashSet("biz_account"));
    System.out.println("effectedGoalRuleList = " + JSON.toJSONString(effectedGoalRuleList));
  }

  @Test
  public void testConsumerMsg() {
    Uninterruptibles.sleepUninterruptibly(1, TimeUnit.HOURS);
  }

  @Test
  public void testNewGoalValueHandler() {
    String opLogStr = "{\"before\":{\"last_modified_time\":\"*************\",\"version\":\"1\",\"sys_modified_time\":\"****************\"},\"meta\":{\"schema\":\"public\",\"op\":\"U\",\"time\":\"2024-01-12 15:02:33.532247\",\"db\":\"bi_112\",\"table\":\"goal_value\"},\"keys\":{\"tenant_id\":\"85529\",\"id\":\"659fc1922dd09100012911ef\"},\"object_describe_api_name\":\"GoalValueObj\",\"after\":{\"last_modified_time\":\"*************\",\"annual_value\":\"4.29\",\"version\":\"2\",\"sys_modified_time\":\"****************\"}}";
    String opLogGoalValueObjStr = "{\"before\":{\"last_modified_time\":\"*************\",\"sys_modified_time\":\"****************\"},\"meta\":{\"schema\":\"public\",\"op\":\"U\",\"time\":\"2024-04-21 14:10:21.818364\",\"db\":\"bi_112\",\"table\":\"goal_value_obj\"},\"keys\":{\"tenant_id\":\"85529\",\"id\":\"659fc1922dd09100012911ef\"},\"object_describe_api_name\":\"GoalValueObj\",\"after\":{\"last_modified_time\":\"*************\",\"sys_modified_time\":\"****************\"}}";
    String opLogGoalValueObjQuarterStr = "{\"meta\":{\"schema\":\"public\",\"op\":\"U\",\"time\":\"2024-04-22 12:58:11.572414\",\"db\":\"bi_112\",\"table\":\"goal_value_obj_quarter\"},\"keys\":{\"tenant_id\":\"85529\",\"id\":\"6625ee6346773606522531d0\"},\"object_describe_api_name\":\"GoalValueObj\"}";
    OpLog opLog = JSON.parseObject(opLogGoalValueObjStr, OpLog.class);
    newGoalValueHandler.batchHandler(Lists.newArrayList(opLog));
  }

  @Test
  public void testMultiGoalValueHandler() {
    String opLogGoalValueObjStr = "{\"before\":{\"last_modified_time\":\"*************\",\"sys_modified_time\":\"****************\"},\"meta\":{\"schema\":\"public\",\"op\":\"U\",\"time\":\"2024-04-21 14:10:21.818364\",\"db\":\"bi_112\",\"table\":\"goal_value_obj\"},\"keys\":{\"tenant_id\":\"85529\",\"id\":\"65a0e82927381e318515ed61\"},\"object_describe_api_name\":\"GoalValueObj\",\"after\":{\"last_modified_time\":\"*************\",\"sys_modified_time\":\"****************\"}}";
    String opLogBizAccountStr = "{\"after\":{\"data_auth_code\":\"204c1c51ae36ba83\",\"data_auth_id\":\"7616099\",\"sys_modified_time\":\"****************\"},\"before\":{\"data_auth_code\":\"1efa0ea188c0f195\",\"data_auth_id\":\"7616098\",\"sys_modified_time\":\"****************\"},\"dB\":\"bi_112\",\"keys\":{\"tenant_id\":\"85529\",\"id\":\"65a66cf07abff900017d67df\"},\"kind\":\"update\",\"meta\":{\"schema\":\"public\",\"op\":\"U\",\"time\":\"2024-04-01 15:47:35.025283\",\"db\":\"bi_112\",\"table\":\"biz_account\"},\"msgId\":\"0A7C573F0001164E5E3603638B055C87\",\"object_describe_api_name\":\"AccountObj\",\"retryTimes\":0,\"schema\":\"public\",\"storeTimeStamp\":*************,\"success\":1,\"table\":\"biz_account\",\"tenantId\":\"85529\",\"topic\":\"bi_oplog_normal\",\"touch\":false}";
    OpLog opLog = JSON.parseObject(opLogBizAccountStr, OpLog.class);
    multiDimGoalHandler.batchHandler(Lists.newArrayList(opLog));
  }

  @Test
  public void givenJanuaryDate_whenConverted_thenReturnsFirstQuarter() {
    String actionDate = "********";
    // 找出月的第一天属于的季度
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    LocalDate date = LocalDate.parse(actionDate, formatter);
    int year = date.getYear();
    int monthValue = date.getMonthValue();
    int quarter = (monthValue - 1) / 3 + 1;
    System.out.println("year + \"Q\" + quarter = " + year + "Q" + quarter);
  }

  @Test
  public void testDispatcher() throws Exception {
    String goalValueMsg = "{\"meta\":{\"schema\":\"public\",\"op\":\"I\",\"time\":\"2023-09-12 16:32:07.056258\",\"db\":\"bi_112\",\"table\":\"goal_value\"},\"keys\":{\"tenant_id\":\"85529\",\"id\":\"650021f7b6c7d2000159606a\"},\"object_describe_api_name\":\"GoalValueObj\"}";
    String bizAccountMsg = "{\"before\":{\"owner\":\"1000\",\"out_data_auth_id\":\"315123\",\"last_modified_time\":\"*************\",\"data_auth_code\":\"19a6ce52bc03d17c\",\"transfer_count\":\"1\",\"out_data_auth_code\":\"432bfabeaf3d807e\",\"data_auth_id\":\"7524861\",\"owner_modified_time\":\"*************\",\"version\":\"2\",\"sys_modified_time\":\"****************\",\"claimed_time\":\"*************\"},\"meta\":{\"schema\":\"public\",\"op\":\"U\",\"time\":\"2023-09-12 17:47:37.11992\",\"db\":\"bi_112\",\"table\":\"biz_account\"},\"keys\":{\"tenant_id\":\"85529\",\"id\":\"64ffd9a74cc4f20001eac46c\"},\"object_describe_api_name\":\"AccountObj\",\"after\":{\"owner\":\"1151\",\"recycled_reason\":\"管理员（上级）收回\",\"out_data_auth_id\":\"499763\",\"last_modified_time\":\"*************\",\"data_auth_code\":\"e7b7de1a7ffe228f\",\"transfer_count\":\"2\",\"out_data_auth_code\":\"64325e5ab9d97fcd\",\"data_auth_id\":\"7524958\",\"owner_modified_time\":\"*************\",\"version\":\"3\",\"sys_modified_time\":\"****************\",\"claimed_time\":\"*************\"}}";
    OpLog opLog = JSON.parseObject(bizAccountMsg, OpLog.class);
    DispatcherManager dispatcherManager = DispatcherManager.createInstance(paasDataParseListener);
    dispatcherManager.appendEvent(opLog);
    dispatcherManager.runEvents();
  }

  @Test
  public void testPaasGoalRuleDao() {
    Map<String, PaasGoalRuleDao.GoalRuleCheckLevelDO> a = paasGoalRuleDao.setTenantId("71570")
                                                                         .findPaasGoalRuleByIds(Sets.newHashSet("635243910b835f0001ec7e06", "63524a100b835f0001ec9b90", "6280b66b49b0a5000127ddf9"));
    System.out.println(JSON.toJSONString(a));
  }

  @Test
  public void testNewGoalValue() {
    List<GoalValuePlus> goalValuePlusList = goalMapper.setTenantId("71570")
                                                      .queryGoalValueById("71570", "624426e5d10f560001ca1f29");
    System.out.println("size================" + goalValuePlusList.size());
    if (!goalValuePlusList.isEmpty()) {
      System.out.println(JSON.toJSONString(goalValuePlusList.get(0)));
    } else {
      System.out.println("======================null=============");
    }

  }

  @Test
  public void updateGoalValue() {
    goalValueUpdateHandlerService.updateGoalValue("71570", "5fab5beb52e8cf0001e3280d", "5f8fe0b2e2e13d0001d8ae82", "owner_department|owner|name", "name", "3", "biz_account", "asdasdas", false);
  }

  @Test
  public void handlerOplogMessage() {
    String json = "{\"before\":{\"is_deleted\":\"0\",\"last_modified_time\":\"2021-05-31 09:59:44.864\",\"version\":\"1\"},\"meta\":{\"schema\":\"public\",\"op\":\"U\",\"time\":\"2021-05-31 15:22:53.609474\",\"db\":\"bi_112\",\"table\":\"object_data\"},\"keys\":{\"tenant_id\":\"78612\",\"_id\":\"60b44310ff3eaf00014cdf55\"},\"object_describe_api_name\":\"checkins_data_udef\",\"value0\":\"60b442fafcee0400019ff766\",\"after\":{\"is_deleted\":\"1\",\"last_modified_time\":\"2021-05-31 15:22:53.351\",\"version\":\"2\"}}";
    OpLog oplog = JSONObject.parseObject(json, new TypeReference<OpLog>() {
    });
    String topic = "bi_oplog_normal";
    goalValueUpdateHandlerService.handlerOplogMessage(oplog);
  }

  @Test
  public void testGoalRule() throws InterruptedException {
    // goalValueDao.batchEtlGoalValueByTenantIds(Lists.newArrayList("82958"));
    goalValueDao.etlGoalValueByGaolRuleId("85145", "64da10b903fcb200015c37dc");
    // goalValueDao.etlGoalValueByGaolRuleId("85145","680b66eb96f7a30001e530a3");
    // 子目标
    // goalValueDao.etlGoalValueByGaolRuleId("85145","6581038c9caf5300011f3c19");
  }

  @Test
  public void testGetGoalValueFromPaas() {
    List<GoalValuePlus> goalValuePluses = paasGoalRuleDao.batchQueryGoalValueByGoalRuleId("85529", "659fbc392dd0910001290829", "0", 500);
    System.out.println("goalValuePluses = " + JSON.toJSONString(goalValuePluses));
  }

  @Test
  public void testStartQuart() {
    int month = 7; // 假设输入的月份为7（七月）
    int startMonth = 8; // 起始月份为2（二月）
    int adjustedMonth = (month - startMonth + 12) % 12;
    int quarter = (adjustedMonth / 3) + 1;
    System.out.println("quarter = " + quarter);
  }
}
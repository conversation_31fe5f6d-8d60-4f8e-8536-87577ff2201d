package com.fxiaoke.bi.paas2bi.transfer.multilangrecord;

import com.fxiaoke.bi.paas2bi.transfer.bean.OpLog;
import com.fxiaoke.bi.paas2bi.transfer.bean.ViewChangeMessage;
import com.fxiaoke.bi.paas2bi.transfer.mapper.bi.MultiRecordTableMapper;
import com.fxiaoke.bi.paas2bi.transfer.producer.ViewChangeProducer;
import com.fxiaoke.bi.paas2bi.transfer.service.BIPGDataSource;
import com.fxiaoke.bi.statistic.common.utils.GrayManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 多语言处理服务
 */
@Slf4j
@Service
public class MultiLangService {

  private final MultiLangHistoryService multiLangHistoryService;
  private final BIPGDataSource bipgDataSource;
  private final MultiRecordTableMapper multiRecordTableMapper;
  private final ViewChangeProducer viewChangeProducer;

  public MultiLangService(MultiLangHistoryService multiLangHistoryService,
                          BIPGDataSource bipgDataSource,
                          MultiRecordTableMapper multiRecordTableMapper,
                          ViewChangeProducer viewChangeProducer) {
    this.multiLangHistoryService = multiLangHistoryService;
    this.bipgDataSource = bipgDataSource;
    this.multiRecordTableMapper = multiRecordTableMapper;
    this.viewChangeProducer = viewChangeProducer;
  }

  /**
   * 处理多语言记录
   */
  public void processMultiLangRecord(String tenantId, OpLog opLog) {
    // 获取schema名称
    String schemaName = "public";
    if (bipgDataSource.isSchema(tenantId)) {
      schemaName = "sch_" + tenantId;
    }
    String table = opLog.getTable();
    multiLangHistoryService.processTableById(tenantId, schemaName, table, opLog.getObject_describe_api_name(), opLog.findPrimKey());
    if (GrayManager.isAllowByRule("stat_view_enable_multi_lang_eis", tenantId)) {
      triggerMultiLangAggCalc(opLog);
    }
  }

  /**
   * 批量处理多语言记录
   *
   * @param tenantId 租户ID
   * @param opLogs   操作日志列表
   */
  public void processMultiLangRecordsBatch(String tenantId, List<OpLog> opLogs) {
    if (CollectionUtils.isEmpty(opLogs)) {
      log.warn("批量处理多语言记录失败: opLogs为空, tenantId: {}", tenantId);
      return;
    }

    // 获取schema名称
    String schemaName = "public";
    if (bipgDataSource.isSchema(tenantId)) {
      schemaName = "sch_" + tenantId;
    }

    // 按表名分组进行批量处理
    Map<String, List<OpLog>> tableGroupMap = opLogs.stream().collect(Collectors.groupingBy(OpLog::getTable));

    for (Map.Entry<String, List<OpLog>> entry : tableGroupMap.entrySet()) {
      String tableName = entry.getKey();
      List<OpLog> tableOpLogs = entry.getValue();

      try {
        // 构建批量处理请求
        List<MultiLangHistoryService.BatchProcessRequest> batchRequests = tableOpLogs.stream()
                                                                                     .map(opLog -> new MultiLangHistoryService.BatchProcessRequest(opLog.getObject_describe_api_name(), opLog.findPrimKey()))
                                                                                     .collect(Collectors.toList());

        // 批量处理表记录
        multiLangHistoryService.processTableByIds(tenantId, schemaName, tableName, batchRequests);

        // 如果启用了多语言统计图计算，批量触发
        if (GrayManager.isAllowByRule("stat_view_enable_multi_lang_eis", tenantId)) {
          triggerMultiLangAggCalcBatch(tableOpLogs);
        }

        log.debug("批量处理多语言记录完成, tenantId: {}, table: {}, count: {}", tenantId, tableName, tableOpLogs.size());
      } catch (Exception e) {
        log.error("批量处理多语言记录异常, tenantId: {}, table: {}, count: {}", tenantId, tableName, tableOpLogs.size(), e);
        // 如果批量处理失败，可以考虑回退到单条处理
        log.info("回退到单条处理模式, tenantId: {}, table: {}", tenantId, tableName);
        for (OpLog opLog : tableOpLogs) {
          try {
            multiLangHistoryService.processTableById(tenantId, schemaName, opLog.getTable(), opLog.getObject_describe_api_name(), opLog.findPrimKey());
            if (GrayManager.isAllowByRule("stat_view_enable_multi_lang_eis", tenantId)) {
              triggerMultiLangAggCalc(opLog);
            }
          } catch (Exception singleProcessException) {
            log.error("单条处理多语言记录异常, tenantId: {}, table: {}, apiName: {}, msgId: {}", tenantId, opLog.getTable(), opLog.getObject_describe_api_name(), opLog.getMsgId(), singleProcessException);
          }
        }
      }
    }
  }

  /**
   * 批量触发多语言统计图计算
   *
   * @param opLogs 操作日志列表
   */
  private void triggerMultiLangAggCalcBatch(List<OpLog> opLogs) {
    for (OpLog opLog : opLogs) {
      try {
        triggerMultiLangAggCalc(opLog);
      } catch (Exception e) {
        log.error("批量触发多语言统计图计算异常, tenantId: {}, objectDescribeApiName: {}", opLog.getTenantId(), opLog.getObject_describe_api_name(), e);
      }
    }
  }

  /**
   * 触发多语言统计图计算
   * 1. 获取对象支持的语言
   * 2. 查询受影响的统计图
   * 3. 比较统计图和对象支持的语言
   * 4. 发送MQ消息通知计算
   *
   * @param opLog 操作日志
   */
  public void triggerMultiLangAggCalc(OpLog opLog) {
    String tenantId = opLog.getTenantId();
    String objectDescribeApiName = opLog.getObject_describe_api_name();

    if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(objectDescribeApiName)) {
      log.warn("触发多语言统计图计算失败: tenantId或objectDescribeApiName为空, tenantId: {}, objectDescribeApiName: {}", tenantId, objectDescribeApiName);
      return;
    }
    try {
      // 1. 获取对象支持的语言
      List<String> objectSupportedLangs = multiRecordTableMapper.setTenantId(tenantId)
                                                                .findObjectSupportedLangs(tenantId, objectDescribeApiName);
      if (CollectionUtils.isEmpty(objectSupportedLangs)) {
        log.info("对象不支持多语言, tenantId: {}, objectDescribeApiName: {}", tenantId, objectDescribeApiName);
        return;
      }

      // 2. 查询受影响的统计图 - 这里不再使用百分号，因为SQL中已经使用JSON函数进行精确匹配
      List<Map<String, Object>> affectedViews = multiRecordTableMapper.setTenantId(tenantId)
                                                                      .findAffectedViews(tenantId, objectDescribeApiName);
      if (CollectionUtils.isEmpty(affectedViews)) {
        log.info("没有受影响的统计图, tenantId: {}, objectDescribeApiName: {}", tenantId, objectDescribeApiName);
        return;
      }
      // 3. 比较统计图和对象支持的语言，找出需要新增支持的语言
      for (Map<String, Object> view : affectedViews) {
        String viewId = Objects.toString(view.get("source_id"), "");
        String viewLang = Objects.toString(view.get("lang"), "");
        // 如果统计图的语言为空或者为default，则处理成default
        if (StringUtils.isBlank(viewLang)) {
          viewLang = "default";
        }
        // 找出统计图需要新增支持的语言
        List<String> unsupportedLangs = new ArrayList<>(objectSupportedLangs);
        unsupportedLangs.remove(viewLang);
        if (CollectionUtils.isEmpty(unsupportedLangs)) {
          continue;
        }
        // 4. 发送MQ消息通知计算
        for (String lang : unsupportedLangs) {
          // 创建消息对象
          ViewChangeMessage message = ViewChangeMessage.builder()
                                                       .tenantId(tenantId)
                                                       .sourceId(viewId)
                                                       .sourceType("0")
                                                       .lang(lang)
                                                       .build();
          // 发送消息
          viewChangeProducer.sendMsg(message);
        }
      }
    } catch (Exception e) {
      log.error("触发多语言统计图计算异常, tenantId: {}, objectDescribeApiName: {}", tenantId, objectDescribeApiName, e);
    }
  }
}
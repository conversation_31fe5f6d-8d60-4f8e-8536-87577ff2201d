package com.fxiaoke.bi.paas2bi.transfer.mapper.bi;

import com.fxiaoke.bi.paas2bi.transfer.entity.MultiLangTableEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface MultiRecordTableMapper extends ITenant<MultiRecordTableMapper> {


  /**
   * 插入多语言表记录
   *
   * @param entity 实体对象
   * @return 影响行数
   */
  int insert(MultiLangTableEntity entity);

  /**
   * 批量插入多语言表记录
   *
   * @param entities 实体对象列表
   * @return 影响行数
   */
  int batchInsert(@Param("entities") List<MultiLangTableEntity> entities);

  /**
   * 查询对象支持的语言列表
   *
   * @param tenantId              租户ID
   * @param objectDescribeApiName 对象描述API名称
   * @return 语言列表
   */
  List<String> findObjectSupportedLangs(@Param("tenantId") String tenantId,
                                        @Param("objectDescribeApiName") String objectDescribeApiName);

  /**
   * 查询受影响的统计图
   *
   * @param tenantId              租户ID
   * @param objectDescribeApiName 对象描述API名称模式
   * @return 统计图列表
   */
  @SuppressWarnings("MybatisXMapperMethodInspection")
  List<Map<String, Object>> findAffectedViews(@Param("tenantId") String tenantId,
                                              @Param("objectDescribeApiName") String objectDescribeApiName);
}

package com.fxiaoke.bi.paas2bi.transfer.mapper.bi;

import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

/**
 * Created by jief on 2021/9/24.
 */
@Component
public interface OrgDeptMapper extends ITenant<OrgDeptMapper> {

  @Update("update org_dept set dept_path = array_append(string_to_array(ltree2text(dept_parent_path),'.')::integer[],dept_id::integer)  where tenant_id = #{tenantId}")
  int updateDeptPath(@Param("tenantId") String tenantId);
}

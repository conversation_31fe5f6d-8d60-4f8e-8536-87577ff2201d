package com.fxiaoke.bi.paas2bi.transfer.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.paas2bi.transfer.bean.GoalRule;
import com.fxiaoke.bi.paas2bi.transfer.mapper.paas.GoalMapper;
import com.fxiaoke.bi.paas2bi.transfer.pojo.CheckDimensionField;
import com.fxiaoke.common.Pair;
import com.fxiaoke.notifier.support.NotifierClient;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fxiaoke.bi.paas2bi.transfer.utils.Context.GOAL_RULE_CHANGE_ROOM;

/**
 * <AUTHOR>
 * @date 2023/8/3
 */
@Slf4j
@Service
public class GoalRuleCacheService {

  @Resource
  private GoalMapper goalMapper;

  @PostConstruct
  public void init() {
    NotifierClient.register(GOAL_RULE_CHANGE_ROOM, (message) -> {
      log.info("goal_rule_change_room received, msg: {}", message);
      JSONObject jsonObject = JSON.parseObject(message.getContent());
      String tenantId = jsonObject.getString("tenantId");
      List<String> apiList = jsonObject.getObject("apiName", new TypeReference<List<String>>() {
      });
      TENANT_API_GOAL_RULE_CACHE.invalidateAll(apiList.stream()
                                                      .map(apiName -> Pair.build(tenantId, apiName))
                                                      .collect(Collectors.toList()));
      log.info("invalidate goal rule api cache success! tenant:{},apiList:{}", tenantId, JSON.toJSONString(apiList));
    });
  }

  /**
   * key: 企业id，对象名; value: 关联的规则 goal_rule id
   */
  private final Cache<Pair<String, String>, List<CheckDimensionField>> TENANT_API_GOAL_RULE_CACHE = CacheBuilder.newBuilder()
                                                                                                                .maximumSize(4000)
                                                                                                                .expireAfterWrite(24, TimeUnit.HOURS)
                                                                                                                .build();

  /**
   * 获取多个对象受影响的规则
   */
  public List<CheckDimensionField> getEffectedGoalRuleList(String tenantId, Set<String> apiNameSet) {
    ArrayList<CheckDimensionField> checkDimensionFieldList = new ArrayList<>();
    apiNameSet.forEach(apiName -> {
      List<CheckDimensionField> effectedGoalRule = getEffectedGoalRule(Pair.build(tenantId, apiName));
      if (CollectionUtils.isEmpty(effectedGoalRule)) {
        return;
      }
      checkDimensionFieldList.addAll(effectedGoalRule);
    });
    return checkDimensionFieldList;
  }

  /**
   * 监听 goal_rule 变更，如果新增或减少需要计算 data_auth_code 的对象需要更新缓存
   */
  public List<CheckDimensionField> getEffectedGoalRule(Pair<String, String> tenantApi) {
    try {
      return TENANT_API_GOAL_RULE_CACHE.get(tenantApi, () -> {
        String tenantId = tenantApi.first;
        String apiName = tenantApi.second;
        List<GoalRule> goalRuleList = goalMapper.setTenantId(tenantId).findAllRuleCheckDimensionsByEi(tenantId);
        ArrayList<CheckDimensionField> caches = new ArrayList<>();
        for (GoalRule goalRule : goalRuleList) {
          String checkDimensionFields = goalRule.getCheckDimensionFields();
          // 需要存 data_auth_code 的规则
          if (checkDimensionFields.contains("auth_code_field_location")) {
            List<CheckDimensionField> checkDimensionFieldList = JSON.parseArray(checkDimensionFields, CheckDimensionField.class);
            checkDimensionFieldList.forEach(checkDimensionField -> {
              if (StringUtils.isNotBlank(checkDimensionField.getAuthCodeFieldLocation()) &&
                  apiName.equals(checkDimensionField.getAuthDescribeApiName())) {
                checkDimensionField.setId(goalRule.getId());
                checkDimensionField.setCheckCycle(goalRule.getCheckCycle());
                caches.add(checkDimensionField);
              }
            });
          }
        }
        return caches;
      });
    } catch (ExecutionException e) {
      log.error("load goal_rule cache error! tenant:{}, apiName:{}.", tenantApi.first, tenantApi.second, e);
      throw new RuntimeException(e);
    }
  }
}

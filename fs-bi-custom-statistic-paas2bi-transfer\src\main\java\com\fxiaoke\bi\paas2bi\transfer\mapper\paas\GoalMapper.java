package com.fxiaoke.bi.paas2bi.transfer.mapper.paas;

import com.fxiaoke.bi.paas2bi.transfer.bean.GoalRule;
import com.fxiaoke.bi.paas2bi.transfer.bean.GoalValueEntity;
import com.fxiaoke.bi.paas2bi.transfer.pojo.GoalRuleBO;
import com.fxiaoke.bi.paas2bi.transfer.pojo.GoalValuePlus;
import com.github.mybatis.mapper.ICrudPaginationMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface GoalMapper extends ICrudPaginationMapper<GoalValueEntity>, ITenant<GoalMapper> {


  //分页查询goal_value 表数据
  @Select("select * from goal_value where tenant_id=#{tenantId} and is_deleted=0 and id > #{startId} order by id asc limit #{limit}")
  @Results({@Result(column = "package", property = "packageName")})
  List<GoalValueEntity> queryGoalValueByLimit(@Param("tenantId") String tenantId,
                                              @Param("startId") String startId,
                                              @Param("limit") Integer limit);

  //根据id查询 goal_value 数据
  @Select("select * from goal_value where tenant_id=#{tenantId} and is_deleted=0 and id =#{id}")
  @Results({@Result(column = "package", property = "packageName")})
  GoalValueEntity getGoalValueById(@Param("tenantId") String tenantId, @Param("id") String id);

  //查询目标值
  @Select(
    " SELECT t0.*,t1.start_month,t1.start_week,t1.start_quarter,t1.check_level_field_api_name as check_level_field_api_names,t1.check_level_type, t1.check_cycle,t1.id as gid, t1.check_dimension_fields as rule_check_dimension_fields " +
    " FROM  goal_value t0 left join goal_rule t1 ON t0.tenant_id = t1.tenant_id AND t0.goal_rule_id = t1.ID " +
    " WHERE  t0.tenant_id = #{tenantId} AND t0.id = #{id} ")
  @Results({@Result(column = "package", property = "packageName")})
  List<GoalValuePlus> queryGoalValueById(@Param("tenantId") String tenantId, @Param("id") String goalValueId);

  //批量查询目标值
  @Select(
    " SELECT t0.*,t1.start_month,t1.start_week,t1.start_quarter,t1.check_level_field_api_name as check_level_field_api_names,t1.check_level_type " +
    " FROM  goal_value t0, goal_rule t1 " +
    " WHERE t0.tenant_id = #{tenantId} AND t0.id>#{startId}  AND t0.tenant_id = t1.tenant_id AND t0.goal_rule_id = t1.ID order by t0.id asc limit #{batchSize}")
  @Results({@Result(column = "package", property = "packageName")})
  List<GoalValuePlus> batchQueryGoalValue(@Param("tenantId") String tenantId,
                                          @Param("startId") String goalValueId,
                                          @Param("batchSize") int batchSize);

  //根据goalRule批量查询目标值
  @Select(
    " SELECT t0.*,t1.start_month,t1.start_week,t1.start_quarter,t1.check_level_field_api_name as check_level_field_api_names,t1.check_level_type, t1.check_cycle, t1.check_dimension_fields as rule_check_dimension_fields" +
    " FROM  goal_value t0 left join " +
    " goal_rule t1 on t0.tenant_id = t1.tenant_id AND t0.goal_rule_id = t1.ID and t1.tenant_id=#{tenantId}" +
    " WHERE t0.tenant_id = #{tenantId} and t1.id=#{goalRuleId} AND t0.id>#{startId}  order by t0.id asc limit #{batchSize}")
  @Results({@Result(column = "package", property = "packageName")})
  List<GoalValuePlus> batchQueryGoalValueByGoalRuleId(@Param("tenantId") String tenantId,
                                                      @Param("startId") String goalValueId,
                                                      @Param("goalRuleId") String goalRuleId,
                                                      @Param("batchSize") int batchSize);

  @Select("select gv.*,gr.start_month,gr.start_week,gr.start_quarter," +
          " gr.check_level_field_api_name as check_level_field_api_names," + " gr.check_level_type " +
          " from goal_value gv left join goal_rule gr \n" +
          " on (gv.tenant_id=gr.tenant_id and gv.goal_rule_id=gr.id) \n" + " WHERE\n" + " gv.tenant_id=#{tenantId} \n" +
          " and gv.check_object_id=#{objectId} \n" + " and gr.check_level_type !='5'\n" +
          " and gr.check_level_type !='2'\n" + " and gr.check_level_type !='0'\n" +
          " and gr.check_level_type is not null" + " and gr.theme_api_name != 'MultiDimGoal'" +
          " and gv.check_level_field_api_name is not null\n" + " and gv.goal_type=#{apiName}")
  List<GoalValuePlus> getAllEffectGoalValues(@Param("tenantId") String tenantId,
                                             @Param("objectId") String objectId,
                                             @Param("apiName") String apiName);

  @Select("SELECT * FROM goal_value WHERE tenant_id = #{tenantId} AND goal_rule_id = #{ruleId} AND id > #{id} AND is_deleted = 0 ORDER BY id LIMIT #{pageSize}")
  List<GoalValuePlus> getGoalValueObjByRuleId(@Param("tenantId") String tenantId,
                                              @Param("ruleId") String ruleId,
                                              @Param("id") String id,
                                              @Param("pageSize") Integer pageSize);

  @Select("SELECT DISTINCT oid FROM ${tableName} WHERE tenant_id = #{tenantId} AND is_deleted = 0 AND oid NOT IN (SELECT DISTINCT id FROM goal_value WHERE tenant_id = #{tenantId} AND is_deleted = 0)")
  List<String> getInValidGoalValueIdList(@Param("tenantId") String tenantId, @Param("tableName") String tableName);

  @Select("SELECT DISTINCT oid FROM ${tableName} WHERE tenant_id = #{tenantId} AND goal_rule_id ${goalRuleIds} AND is_deleted = 0 AND oid NOT IN (SELECT DISTINCT id FROM goal_value WHERE tenant_id = #{tenantId} AND goal_rule_id ${goalRuleIds} AND is_deleted = 0)")
  List<String> getInvalidGoalValueIdListByRuleId(@Param("tenantId") String tenantId,
                                                 @Param("tableName") String tableName,
                                                 @Param("goalRuleIds") String goalRuleIds);


  @Select("SELECT id,tenant_id,name,check_object_api_name,check_cycle,check_level_field_api_name,check_level_type FROM goal_rule WHERE tenant_id=#{tenantId} AND id=#{id} AND is_deleted=0")
  GoalRuleBO findCheckCycle(@Param("tenantId") String tenantId, @Param("id") String id);

  @Select("SELECT id FROM goal_rule WHERE tenant_id=#{tenantId} AND is_deleted=0")
  List<String> findAllGoalRuleByTenantId(@Param("tenantId") String tenantId);

  /**
   * 找到这个企业下的多维目标规则
   */
  @Select("SELECT id, check_dimension_fields, check_cycle FROM goal_rule WHERE tenant_id=#{tenantId} AND theme_api_name = 'MultiDimGoal' AND check_level_type = '0' AND is_deleted=0 ")
  List<GoalRule> findAllRuleCheckDimensionsByEi(@Param("tenantId") String tenantId);
}

package com.fxiaoke.bi.paas2bi.transfer.dispatcher.manger;

import com.fxiaoke.bi.paas2bi.transfer.bean.OpLog;
import com.fxiaoke.bi.paas2bi.transfer.dispatcher.listener.EventListener;
import com.fxiaoke.bi.paas2bi.transfer.service.PaasDataParseListener;
import com.fxiaoke.bi.statistic.common.utils.GrayManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class DispatcherManager {

  private final PaasDataParseListener paasDataParseListener;

  private final Map<EventListener<OpLog>, List<OpLog>> listenerListMap = Maps.newHashMap();

  public static DispatcherManager createInstance(PaasDataParseListener paasDataParseListener) {
    return new DispatcherManager(paasDataParseListener);
  }

  private DispatcherManager(PaasDataParseListener paasDataParseListener) {
    this.paasDataParseListener = paasDataParseListener;
  }

  public void appendEvent(OpLog opLog) {
    String tableName = opLog.getTable();
    String tags = opLog.getTable();
    if (tags != null && tags.endsWith("_lang") && GrayManager.isAllowByRule("multiLangRecordUseGoalConsumer", opLog.getTenantId())) {
      listenerListMap.computeIfAbsent(paasDataParseListener.getMultiLangRecordHandler(), key -> Lists.newArrayList())
                     .add(opLog);
      return;
    }

    switch (tableName) {
      case "goal_value": {
        listenerListMap.computeIfAbsent(paasDataParseListener.getGoalValueEventListener(), key -> Lists.newArrayList())
                       .add(opLog);
        break;
      }
      case "goal_rule": {
        listenerListMap.computeIfAbsent(paasDataParseListener.getGoalRuleEventListener(), key -> Lists.newArrayList())
                       .add(opLog);
        break;
      }
      case "mt_country_area_info": {
        listenerListMap.computeIfAbsent(paasDataParseListener.getAreaInfoEventListener(), key -> Lists.newArrayList())
                       .add(opLog);
        break;
      }
      case "product_category": {
        listenerListMap.computeIfAbsent(paasDataParseListener.getProductCategoryEventListener(), key -> Lists.newArrayList())
                       .add(opLog);
        break;
      }
      default: {
        listenerListMap.computeIfAbsent(paasDataParseListener.getCheckLevelFieldListener(), key -> Lists.newArrayList())
                       .add(opLog);
        listenerListMap.computeIfAbsent(paasDataParseListener.getMultiDimGoalEventListener(), key -> Lists.newArrayList())
                       .add(opLog);
        break;
      }
    }
  }

  /**
   * 执行事件列表
   */
  public void runEvents() throws Exception {
    for (Map.Entry<EventListener<OpLog>, List<OpLog>> entry : listenerListMap.entrySet()) {
      EventListener<OpLog> key = entry.getKey();
      List<OpLog> value = entry.getValue();
      key.onTrigger(value);
    }
  }
}

package com.fxiaoke.bi.paas2bi.transfer.controller;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.paas2bi.transfer.bean.OpLog;
import com.fxiaoke.bi.paas2bi.transfer.bean.SysOffLineArg;
import com.fxiaoke.bi.paas2bi.transfer.consumer.MultiLangRecordConsumer;
import com.fxiaoke.bi.paas2bi.transfer.dao.GoalValueDao;
import com.fxiaoke.bi.paas2bi.transfer.handler.GoalValueHandler;
import com.fxiaoke.bi.paas2bi.transfer.handler.RoutSplitServiceHandler;
import com.fxiaoke.bi.paas2bi.transfer.http.HttpResponseResult;
import com.fxiaoke.bi.paas2bi.transfer.multilangrecord.MultiLangHistoryService;
import com.fxiaoke.bi.paas2bi.transfer.service.FiscalChangeService;
import com.fxiaoke.bi.paas2bi.transfer.service.SyncDataEtlService;
import com.fxiaoke.bi.paas2bi.transfer.threadpool.ExecutorPoolService;
import com.fxiaoke.bi.paas2bi.transfer.utils.CommonsUtils;
import com.fxiaoke.bi.paas2bi.transfer.utils.RemotingUtil;
import com.fxiaoke.bi.paas2bi.transfer.vo.BaseResponse;
import io.swagger.annotations.ApiOperation;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by jief on 2019/9/26.
 */
@Controller
@Slf4j(topic = "fs-bi-custom-statistic-paas2bi-transfer")
@Path("/paas2bi/api")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Setter
public class SyncDataController {

  private final RoutSplitServiceHandler routSplitServiceHandler;
  private final ExecutorPoolService executorPoolService;
  private final FiscalChangeService fiscalChangeService;
  private final SyncDataEtlService syncDataEtlService;
  private final GoalValueHandler goalValueHandler;
  private final GoalValueDao goalValueDao;
  private final MultiLangHistoryService multiLangHistoryService;
  private final MultiLangRecordConsumer multiLangRecordConsumer;

  public SyncDataController(RoutSplitServiceHandler routSplitServiceHandler,
                            ExecutorPoolService executorPoolService,
                            FiscalChangeService fiscalChangeService,
                            SyncDataEtlService syncDataEtlService,
                            GoalValueHandler goalValueHandler,
                            GoalValueDao goalValueDao,
                            MultiLangHistoryService multiLangHistoryService,
                            MultiLangRecordConsumer multiLangRecordConsumer) {
    this.routSplitServiceHandler = routSplitServiceHandler;
    this.executorPoolService = executorPoolService;
    this.fiscalChangeService = fiscalChangeService;
    this.syncDataEtlService = syncDataEtlService;
    this.goalValueHandler = goalValueHandler;
    this.goalValueDao = goalValueDao;
    this.multiLangHistoryService = multiLangHistoryService;
    this.multiLangRecordConsumer = multiLangRecordConsumer;
  }

  @GET
  @Path("/ping")
  public HttpResponseResult<String> clear() {
    HttpResponseResult<String> responseResult = new HttpResponseResult<>();
    responseResult.setErrCode(200);
    responseResult.setResult("启动成功");
    return responseResult;
  }

  /**
   * 同步一条id
   */
  @ApiOperation(value = "post", notes = "6.4", response = String.class)
  @POST
  @Path("/syncOffline")
  public HttpResponseResult<String> syncOffline(SysOffLineArg sysOffLineArg) {
    HttpResponseResult<String> responseResult = new HttpResponseResult<>();
    String tenantId = sysOffLineArg.getTenantId();
    String goalValueId = sysOffLineArg.getGoalValueId();
    log.info("syncOffline params: tenant_id: {},goalValueId: {}", tenantId, goalValueId);
    try {
      goalValueDao.upsertGoalValueObj(tenantId, goalValueId);
    } catch (Exception e) {
      responseResult.setErrCode(500);
      responseResult.setErrMessage(e.getMessage());
    }
    return responseResult;
  }

  /**
   * 同步一条goal_rule_id
   */
  @ApiOperation(value = "post", notes = "6.4", response = String.class)
  @POST
  @Path("/syncOfflineByRuleId")
  public HttpResponseResult<String> syncOfflineByRuleId(SysOffLineArg sysOffLineArg) {
    HttpResponseResult<String> responseResult = new HttpResponseResult<>();
    String tenantId = sysOffLineArg.getTenantId();
    String goalRuleId = sysOffLineArg.getGoalRuleId();
    log.info("syncOfflineByRuleId params: tenant_id: {},goalRuleId: {}", tenantId, goalRuleId);
    try {
      goalValueDao.etlGoalValueByGaolRuleId(tenantId, goalRuleId);
    } catch (Exception e) {
      responseResult.setErrCode(500);
      responseResult.setErrMessage(e.getMessage());
    }
    return responseResult;
  }

  /**
   * 传入的是多个企业  一个实例一个线程
   */
  @ApiOperation(value = "post", notes = "6.4", response = String.class)
  @POST
  @Path("/syncOfflineByTenantIdList")
  public HttpResponseResult<String> syncOfflineByTenantIdList(SysOffLineArg sysOffLineArg) {
    HttpResponseResult<String> responseResult = new HttpResponseResult<>();
    try {
      List<String> tenantIds = sysOffLineArg.getTenantIds();
      Map<String, List<String>> pgMasterIpMaps = routSplitServiceHandler.splitTenants(tenantIds);
      for (Map.Entry<String, List<String>> pgMasterIp : pgMasterIpMaps.entrySet()) {
        List<String> tenantIdList = pgMasterIp.getValue();
        executorPoolService.submitOrWait(() -> goalValueDao.batchEtlGoalValueByTenantIds(tenantIdList));
      }
    } catch (Exception e) {
      responseResult.setErrCode(500);
      responseResult.setErrMessage(e.getMessage());
    }
    return responseResult;
  }

  /**
   * 按企业一个一个刷
   */
  @ApiOperation(value = "post", notes = "6.4", response = String.class)
  @POST
  @Path("/syncGoalByTenantIdList")
  public HttpResponseResult<String> syncGoalByTenantIdList(SysOffLineArg sysOffLineArg) {
    HttpResponseResult<String> responseResult = new HttpResponseResult<>();
    try {
      List<String> tenantIds = sysOffLineArg.getTenantIds();
      executorPoolService.submitOrWait(() -> {
        goalValueDao.batchEtlGoalValueByTenantIds(tenantIds);
        log.info("syncGoalByTenantIdList etl success, eis:{}", JSON.toJSONString(tenantIds));
      });
    } catch (Exception e) {
      responseResult.setErrCode(500);
      responseResult.setErrMessage(e.getMessage());
    }
    return responseResult;
  }

  /**
   * 删除对应的规则  每条规则在表中对应12条数据
   */
  @ApiOperation(value = "post", notes = "6.4", response = String.class)
  @POST
  @Path("/syncOfflineDelByRuleId")
  public HttpResponseResult<String> syncOfflineDelByRule(SysOffLineArg sysOffLineArg) {
    HttpResponseResult<String> responseResult = new HttpResponseResult<>();
    try {
      String tenantId = sysOffLineArg.getTenantId();
      String goalValueId = sysOffLineArg.getGoalValueId();
      OpLog opLog = CommonsUtils.getPglogDel(tenantId, goalValueId);
      goalValueHandler.handler(opLog);
    } catch (Exception e) {
      responseResult.setErrCode(500);
      responseResult.setErrMessage(e.getMessage());
    }
    return responseResult;
  }

  /**
   * 根据对应的企业Id进行对goal_value_obj删除数据
   */
  @ApiOperation(value = "post", notes = "6.4", response = String.class)
  @POST
  @Path("/syncOfflineDelByTenantId")
  public HttpResponseResult<String> syncOfflineDelByTenantId(SysOffLineArg sysOffLineArg) {
    HttpResponseResult<String> responseResult = new HttpResponseResult<>();
    try {
      String tenantId = sysOffLineArg.getTenantId();
      goalValueHandler.deleteByTenantId(tenantId);
    } catch (Exception e) {
      responseResult.setErrCode(500);
      responseResult.setErrMessage(e.getMessage());
    }
    return responseResult;
  }

  /**
   * 根据对应的goal_rule_id 删除对应的goal_value_obj 表中的数据
   */
  @ApiOperation(value = "post", notes = "6.4", response = String.class)
  @POST
  @Path("/syncOfflineDelByGoalRuleId")
  public HttpResponseResult<String> syncOfflineDelByGoalRuleId(SysOffLineArg sysOffLineArg) {
    HttpResponseResult<String> responseResult = new HttpResponseResult<>();
    try {
      String tenantId = sysOffLineArg.getTenantId();
      String goalRuleId = sysOffLineArg.getGoalRuleId();
      goalValueHandler.deleteByGoalRuleId(tenantId, goalRuleId);
    } catch (Exception e) {
      responseResult.setErrCode(500);
      responseResult.setErrMessage(e.getMessage());
    }
    return responseResult;
  }

  /**
   * 根据对应的企业Id进行对goal_value_obj更新数据
   */
  @ApiOperation(value = "post", notes = "6.4", response = String.class)
  @POST
  @Path("/syncOfflineUpdateBytennatId")
  public HttpResponseResult<String> syncOfflineUpdateBytennatId(SysOffLineArg sysOffLineArg) {
    HttpResponseResult<String> responseResult = new HttpResponseResult<>();
    try {
      String tenantId = sysOffLineArg.getTenantId();
      goalValueHandler.updateByTenantId(tenantId);
    } catch (Exception e) {
      responseResult.setErrCode(500);
      responseResult.setErrMessage(e.getMessage());
    }
    return responseResult;
  }

  /**
   * 根据对应的企业Id进行对goal_value_obj更新数据
   */
  @ApiOperation(value = "post", notes = "6.4", response = String.class)
  @POST
  @Path("/getIpBytenantId")
  public HttpResponseResult<String> getIp(SysOffLineArg sysOffLineArg) {
    HttpResponseResult<String> responseResult = new HttpResponseResult<>();
    try {
      List<String> tenantIdlist = sysOffLineArg.getTenantIds();
      List<String> tenantIdlistss = new ArrayList<>();
      for (String tenantId : tenantIdlist) {
        String xx = goalValueHandler.getIp(tenantId);
        if (!xx.equals("error")) {
          tenantIdlistss.add(xx);
        }
      }
      responseResult.setResult(JSON.toJSONString(tenantIdlistss));
    } catch (Exception e) {
      responseResult.setErrCode(500);
      responseResult.setErrMessage(e.getMessage());
    }
    return responseResult;
  }

  /**
   * 根据对应的企业Id进行对goal_value_obj财年变更
   */
  @ApiOperation(value = "post", notes = "6.4", response = String.class)
  @POST
  @Path("/etlFiscalYear")
  public HttpResponseResult<String> etlFiscalYear(SysOffLineArg sysOffLineArg) {
    HttpResponseResult<String> responseResult = new HttpResponseResult<>();
    List<String> tenantIds = sysOffLineArg.getTenantIds();
    fiscalChangeService.asyncEtlFiscalChange(tenantIds);
    responseResult.setErrCode(200);
    responseResult.setErrMessage(String.format("submit etl fiscal year host:%s", RemotingUtil.getLocalAddress()));
    return responseResult;
  }

  /**
   * 根据对应的企业Id 强制清洗goal_value 数据
   */
  @ApiOperation(value = "post", notes = "6.4", response = String.class)
  @POST
  @Path("/etlGoalValueByEi")
  public HttpResponseResult<String> etlGoalValueByEi(SysOffLineArg sysOffLineArg) {
    HttpResponseResult<String> responseResult = new HttpResponseResult<>();
    String tenantId = sysOffLineArg.getTenantId();
    List<String> goalValueIds = sysOffLineArg.getGoalValueIds();
    executorPoolService.submitOrWait(() -> {
      try {
        syncDataEtlService.etlByGoalRuleId(tenantId, goalValueIds);
      } catch (Exception e) {
        responseResult.setErrCode(500);
        responseResult.setErrMessage(String.format("submit etlGoalValueByEi error host:%s", RemotingUtil.getLocalAddress()));
      }
    });
    responseResult.setErrCode(200);
    responseResult.setErrMessage(String.format("submit etlGoalValueByEi host:%s", RemotingUtil.getLocalAddress()));
    return responseResult;
  }

  /**
   * 检测 goal_value 删除但是 goal_value_obj 没有删除的情况，需要将这些 goal_value_obj 逻辑删除
   */
  @ApiOperation(value = "post", notes = "6.4", response = String.class)
  @POST
  @Path("/checkGoalValueObj")
  public HttpResponseResult<String> checkGoalValueObj(SysOffLineArg sysOffLineArg) {
    HttpResponseResult<String> responseResult = new HttpResponseResult<>();
    try {
      goalValueDao.checkGoalValueObj(sysOffLineArg);
    } catch (Exception e) {
      log.warn("check goal value obj error", e);
    }
    responseResult.setErrCode(200);
    return responseResult;
  }

  /**
   * 同步历史多语言数据
   *
   * @param sysOffLineArg 租户ID列表
   * @return 同步结果
   */
  @Path("/multilang/history")
  @POST
  @ApiOperation(value = "post", notes = "6.4", response = BaseResponse.class)
  public BaseResponse<Map<String, Object>> syncHistoryMultiLangData(SysOffLineArg sysOffLineArg) {
    List<String> tenantIds = sysOffLineArg.getTenantIds();
    log.info("开始同步历史多语言数据, tenantIds: {}", tenantIds);
    try {
      Map<String, Object> result = multiLangHistoryService.syncHistoryMultiLangData(tenantIds);
      return BaseResponse.success(result);
    } catch (Exception e) {
      log.error("同步历史多语言数据异常", e);
      return BaseResponse.error("同步历史多语言数据失败: " + e.getMessage());
    }
  }

  /**
   * 同步指定企业的指定多语言表数据
   *
   * @param sysOffLineArg 包含租户ID和表名列表
   * @return 同步结果
   */
  @Path("/multilang/specific-tables")
  @POST
  @ApiOperation(value = "同步指定企业的指定多语言表", notes = "6.4", response = BaseResponse.class)
  public BaseResponse<Map<String, Object>> syncSpecificMultiLangTables(SysOffLineArg sysOffLineArg) {
    String tenantId = sysOffLineArg.getTenantId();
    List<String> tableNames = sysOffLineArg.getTableNames();
    log.info("开始同步指定多语言表数据, tenantId: {}, tableNames: {}", tenantId, tableNames);
    try {
      Map<String, Object> result = multiLangHistoryService.syncSpecificMultiLangTables(tenantId, tableNames);
      return BaseResponse.success(result);
    } catch (Exception e) {
      log.error("同步指定多语言表数据异常", e);
      return BaseResponse.error("同步指定多语言表数据失败: " + e.getMessage());
    }
  }

  @Path("/multilang/mqConsumerStatus")
  @POST
  @ApiOperation(value = "", notes = "6.4", response = BaseResponse.class)
  public BaseResponse<String> closeMultiLangRecordMqConsumer(SysOffLineArg sysOffLineArg) {
    try {
      String startOrClose = sysOffLineArg.getStartOrClose();
      if (StringUtils.equals(startOrClose, "start")) {
        multiLangRecordConsumer.start();
        return BaseResponse.success("multiLangMqConsumerStatus start success");
      } else {
        multiLangRecordConsumer.destroy();
        return BaseResponse.success("multiLangMqConsumerStatus close success");
      }
    } catch (Exception e) {
      log.error("closeMultiLangRecordMqConsumer error", e);
      return BaseResponse.error("closeMultiLangRecordMqConsumer error: " + e.getMessage());
    }
  }
}

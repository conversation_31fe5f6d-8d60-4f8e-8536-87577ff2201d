<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fxiaoke.bi.paas2bi.transfer.mapper.bi.MultiRecordTableMapper">

  <!-- 插入记录 -->
  <insert id="insert" parameterType="com.fxiaoke.bi.paas2bi.transfer.entity.MultiLangTableEntity">
    INSERT INTO bi_mt_dim_table_lang (id, tenant_id, api_name, crm_obj_name, lang, is_deleted, created_by, create_time, last_modified_time)
    VALUES (#{id}, #{tenantId}, #{apiName}, #{crmObjName}, #{lang}, #{createdBy}, #{createTime},
            #{lastModifiedTime}) ON CONFLICT (tenant_id, api_name, lang) DO
    UPDATE
      SET api_name = #{apiName}, crm_obj_name = #{crmObjName}, lang = #{lang}, last_modified_time = #{lastModifiedTime}
  </insert>

  <!-- 批量插入记录 -->
  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO bi_mt_dim_table_lang (
    id, tenant_id, api_name, crm_obj_name, lang, created_by, create_time, last_modified_time
    ) VALUES
    <foreach collection="entities" item="item" separator=",">
      (
      #{item.id}, #{item.tenantId}, #{item.apiName}, #{item.crmObjName}, #{item.lang},
      #{item.createdBy}, #{item.createTime}, #{item.lastModifiedTime}
      )
    </foreach>
    ON CONFLICT (tenant_id, api_name, lang) DO NOTHING
  </insert>

  <!-- 查询对象支持的语言列表 -->
  <select id="findObjectSupportedLangs" resultType="java.lang.String">
    SELECT DISTINCT lang
    FROM bi_mt_dim_table_lang
    WHERE tenant_id = #{tenantId}
      AND crm_obj_name = #{objectDescribeApiName}
      AND is_deleted = 0
  </select>

  <!-- 查询受影响的统计图 -->
  <select id="findAffectedViews" resultType="java.util.Map">
    SELECT id, source_id, lang, tenant_id, api_name
    FROM bi_mt_topology_table
    WHERE tenant_id = #{tenantId}
      AND (
      EXISTS (SELECT 1
              FROM JSONB_EACH(CAST(agg_effect_api_names AS jsonb)) AS jb
              WHERE jb.value @> CAST(CONCAT('["', #{objectDescribeApiName}, '_lang"]') AS jsonb))
      )
      AND is_deleted = 0
  </select>
</mapper>
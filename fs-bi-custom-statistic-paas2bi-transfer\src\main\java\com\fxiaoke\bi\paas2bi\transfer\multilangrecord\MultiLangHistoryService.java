package com.fxiaoke.bi.paas2bi.transfer.multilangrecord;

import com.alibaba.fastjson.JSON;
import com.facishare.bi.metadata.context.utils.SysUdfMetadataUtil;
import com.fxiaoke.bi.paas2bi.transfer.bean.ObjectLangRecord;
import com.fxiaoke.bi.paas2bi.transfer.dao.MultiLangTableDao;
import com.fxiaoke.bi.paas2bi.transfer.entity.MultiLangTableEntity;
import com.fxiaoke.bi.paas2bi.transfer.mapper.paas.MultiLangRecordMapper;
import com.fxiaoke.bi.paas2bi.transfer.service.BIPGDataSource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 历史多语言数据处理服务
 */
@Slf4j
@Service
public class MultiLangHistoryService {

  private final MultiLangTableDao multiLangTableDao;
  private final MultiLangRecordMapper multiLangRecordMapper;
  private final BIPGDataSource bipgDataSource;

  public MultiLangHistoryService(MultiLangTableDao multiLangTableDao,
                                 MultiLangRecordMapper multiLangRecordMapper,
                                 BIPGDataSource bipgDataSource) {
    this.multiLangTableDao = multiLangTableDao;
    this.multiLangRecordMapper = multiLangRecordMapper;
    this.bipgDataSource = bipgDataSource;
  }

  /**
   * 同步历史多语言数据
   *
   * @param tenantIds 租户ID列表
   * @return 处理结果统计
   */
  public Map<String, Object> syncHistoryMultiLangData(List<String> tenantIds) {
    if (tenantIds == null || tenantIds.isEmpty()) {
      log.warn("租户ID列表为空，无法同步历史多语言数据");
      return Collections.singletonMap("message", "租户ID列表为空，无法同步历史多语言数据");
    }

    log.info("开始同步历史多语言数据, 租户数量: {}", tenantIds.size());
    Map<String, Object> result = new ConcurrentHashMap<>();
    AtomicInteger totalProcessedTenants = new AtomicInteger(0);
    AtomicInteger totalProcessedTables = new AtomicInteger(0);
    AtomicInteger totalProcessedRecords = new AtomicInteger(0);
    AtomicInteger totalInsertedFields = new AtomicInteger(0);

    for (String tenantId : tenantIds) {
      try {
        Map<String, Object> tenantResult = processTenant(tenantId);
        int processedTables = (int) tenantResult.getOrDefault("processedTables", 0);
        int processedRecords = (int) tenantResult.getOrDefault("processedRecords", 0);
        int insertedFields = (int) tenantResult.getOrDefault("insertedFields", 0);

        totalProcessedTables.addAndGet(processedTables);
        totalProcessedRecords.addAndGet(processedRecords);
        totalInsertedFields.addAndGet(insertedFields);
        totalProcessedTenants.incrementAndGet();

        result.put(tenantId, tenantResult);
      } catch (Exception e) {
        log.error("处理租户 {} 历史多语言数据异常", tenantId, e);
        result.put(tenantId, Collections.singletonMap("error", e.getMessage()));
      }
    }

    // 添加汇总统计
    Map<String, Object> summary = new HashMap<>();
    summary.put("totalProcessedTenants", totalProcessedTenants.get());
    summary.put("totalProcessedTables", totalProcessedTables.get());
    summary.put("totalProcessedRecords", totalProcessedRecords.get());
    summary.put("totalInsertedFields", totalInsertedFields.get());
    result.put("summary", summary);

    log.info("历史多语言数据同步完成, 总处理租户数: {}, 总处理表数: {}, 总处理记录数: {}, 总插入字段数: {}, summary:{}.", totalProcessedTenants.get(), totalProcessedTables.get(), totalProcessedRecords.get(), totalInsertedFields.get(), JSON.toJSONString(summary));
    return result;
  }

  /**
   * 处理单个租户的多语言数据
   *
   * @param tenantId 租户ID
   * @return 处理结果
   */
  private Map<String, Object> processTenant(String tenantId) {
    Map<String, Object> result = new HashMap<>();
    int processedTables = 0;
    int processedRecords = 0;
    int insertedFields = 0;

    try {
      // 获取schema名称
      String schemaName = "public";
      if (bipgDataSource.isSchema(tenantId)) {
        schemaName = "sch_" + tenantId;
      }

      // 查找带有多语言字段的表
      List<String> tables = multiLangRecordMapper.setTenantId(tenantId).findTablesWithMultiLangFields(schemaName);
      if (tables.isEmpty()) {
        log.info("租户 {} 没有带有多语言字段的表", tenantId);
        result.put("message", "没有带有多语言字段的表");
        return result;
      }
      tables.remove("bi_mt_dim_table_lang");
      // tables.remove("org_group_lang");
      log.info("租户 {} 有 {} 个带有多语言字段的表", tenantId, tables.size());

      // 处理每个表
      for (String tableName : tables) {
        try {
          Map<String, Object> tableResult = processTable(tenantId, schemaName, tableName);
          int tableProcessedRecords = (int) tableResult.getOrDefault("processedRecords", 0);
          int tableInsertedFields = (int) tableResult.getOrDefault("insertedFields", 0);

          processedRecords += tableProcessedRecords;
          insertedFields += tableInsertedFields;
          processedTables++;

          log.info("处理租户 {} 表 {} 完成, 处理记录数: {}, 插入字段数: {}", tenantId, tableName, tableProcessedRecords, tableInsertedFields);
        } catch (Exception e) {
          log.error("处理租户 {} 表 {} 异常", tenantId, tableName, e);
        }
      }

      result.put("processedTables", processedTables);
      result.put("processedRecords", processedRecords);
      result.put("insertedFields", insertedFields);
      return result;
    } catch (Exception e) {
      log.error("处理租户 {} 异常", tenantId, e);
      result.put("error", e.getMessage());
      return result;
    }
  }

  /**
   * 处理单个表的多语言数据
   *
   * @param tenantId   租户ID
   * @param schemaName 模式名
   * @param tableName  表名
   * @return 处理结果
   */
  public Map<String, Object> processTable(String tenantId, String schemaName, String tableName) {
    Map<String, Object> result = new HashMap<>();
    int processedRecords = 0;
    int insertedFields = 0;

    try {
      // 查询表中的所有记录ID
      int rows = multiLangRecordMapper.setTenantId(tenantId).findAllRecordIds(schemaName, tableName, tenantId);
      if (rows <= 0) {
        log.info("表 {}.{} 没有符合条件的记录", schemaName, tableName);
        result.put("message", "没有符合条件的记录");
        return result;
      }
      log.info("表 {}.{} 有 {} 个记录需要处理", schemaName, tableName, rows);
      // 按批次处理记录
      try {
        int fieldsCount = processRecord(tenantId, schemaName, tableName);
        if (fieldsCount > 0) {
          insertedFields += fieldsCount;
          processedRecords++;
        }
      } catch (Exception e) {
        log.error("处理记录 {} 多语言对象记录异常", tenantId, e);
      }

      result.put("processedRecords", processedRecords);
      result.put("insertedFields", insertedFields);
      return result;
    } catch (Exception e) {
      log.error("处理表 {}.{} 异常", schemaName, tableName, e);
      result.put("error", e.getMessage());
      return result;
    }
  }

  /**
   * 处理单个记录的多语言数据
   *
   * @param tenantId   租户ID
   * @param schemaName 模式名
   * @param tableName  表名
   * @return 插入的字段数
   */
  private int processRecord(String tenantId, String schemaName, String tableName) {
    try {
      // 查询记录
      List<ObjectLangRecord> records = multiLangRecordMapper.setTenantId(tenantId)
                                                            .findRecordByTenantId(schemaName, tableName, tenantId);
      if (CollectionUtils.isEmpty(records)) {
        log.warn("未找到记录 {}", tenantId);
        return 0;
      }
      List<MultiLangTableEntity> entities = new ArrayList<>();
      for (ObjectLangRecord record : records) {
        // 获取语言代码
        String lang = record.getLang();
        if (StringUtils.isBlank(lang)) {
          log.warn("记录 {} 语言代码为空", tenantId);
          continue;
        }
        // 获取对象描述名称
        String crmObjName = record.getDescribeApiName();
        if (StringUtils.isBlank(crmObjName)) {
          log.warn("记录 {} 对象描述名称为空", tenantId);
          continue;
        }
        MultiLangTableEntity entity = new MultiLangTableEntity();
        entity.setId(ObjectId.get().toString());
        entity.setTenantId(tenantId);
        entity.setApiName(SysUdfMetadataUtil.getBiObjNameByCrmObjName(crmObjName));
        entity.setCrmObjName(crmObjName);
        entity.setLang(lang);
        entity.setIsDeleted(0);
        entity.setCreatedBy("-10000");
        entity.setCreateTime(System.currentTimeMillis());
        entity.setLastModifiedTime(System.currentTimeMillis());
        entities.add(entity);
      }
      multiLangTableDao.batchInsert(entities);
      return entities.size();
    } catch (Exception e) {
      log.error("处理记录 {} 异常", tenantId, e);
      return 0;
    }
  }

  public void processTableById(String tenantId,
                               String schemaName,
                               String tableName,
                               String apiName,
                               String primaryKey) {
    List<Map<String, Object>> records = multiLangRecordMapper.setTenantId(tenantId)
                                                             .findRecordById(schemaName, tableName, tenantId, primaryKey);
    if (CollectionUtils.isEmpty(records)) {
      log.warn("未找到记录 tenantId:{}, table:{}, id:{}", tenantId, tableName, primaryKey);
    }
    List<ObjectLangRecord> recordList = new ArrayList<>();
    records.forEach(data -> {
      ObjectLangRecord record = new ObjectLangRecord();
      record.setTenantId(tenantId);
      record.setLang(String.valueOf(data.get("lang")));
      String describeApiName = Objects.toString(data.getOrDefault("describe_api_name", data.get("object_describe_api_name")), apiName);
      record.setDescribeApiName(describeApiName);
      recordList.add(record);
    });
    upsertLangObjRecord(tenantId, recordList);
  }

  /**
   * 批量处理表记录
   *
   * @param tenantId      租户ID
   * @param schemaName    模式名
   * @param tableName     表名
   * @param batchRequests 批量请求参数列表，包含apiName和primaryKey
   */
  public void processTableByIds(String tenantId,
                                String schemaName,
                                String tableName,
                                List<BatchProcessRequest> batchRequests) {
    if (CollectionUtils.isEmpty(batchRequests)) {
      log.warn("批量处理请求为空 tenantId:{}, table:{}", tenantId, tableName);
      return;
    }

    // 提取所有主键
    List<String> primaryKeys = batchRequests.stream()
                                            .map(BatchProcessRequest::getPrimaryKey)
                                            .collect(Collectors.toList());
    if (CollectionUtils.isEmpty(primaryKeys)) {
      log.info("批量处理请求中没有有效的主键 tenantId:{}, table:{}", tenantId, tableName);
      return;
    }

    // 批量查询记录
    List<Map<String, Object>> records = multiLangRecordMapper.setTenantId(tenantId)
                                                             .findRecordsByIds(schemaName, tableName, tenantId, primaryKeys);
    if (CollectionUtils.isEmpty(records)) {
      log.warn("未找到记录 tenantId:{}, table:{}, ids:{}", tenantId, tableName, primaryKeys);
      return;
    }

    // 构建主键到apiName的映射
    Map<String, String> primaryKeyToApiNameMap = batchRequests.stream()
                                                              .collect(Collectors.toMap(BatchProcessRequest::getPrimaryKey, BatchProcessRequest::getApiName, (existing, replacement) -> replacement));

    // 处理查询结果
    List<ObjectLangRecord> recordList = new ArrayList<>();
    records.forEach(data -> {
      ObjectLangRecord record = new ObjectLangRecord();
      record.setTenantId(tenantId);
      record.setLang(String.valueOf(data.get("lang")));

      String primaryKey = String.valueOf(data.get("id"));
      String defaultApiName = primaryKeyToApiNameMap.get(primaryKey);
      String describeApiName = Objects.toString(data.getOrDefault("describe_api_name", data.get("object_describe_api_name")), defaultApiName);
      record.setDescribeApiName(describeApiName);
      recordList.add(record);
    });

    upsertLangObjRecord(tenantId, recordList);
  }

  /**
   * 批量处理请求参数
   */
  @Data
  @AllArgsConstructor
  public static class BatchProcessRequest {
    private String apiName;
    private final String primaryKey;
  }

  private void upsertLangObjRecord(String tenantId, List<ObjectLangRecord> recordList) {
    List<MultiLangTableEntity> entities = new ArrayList<>();
    try {
      for (ObjectLangRecord record : recordList) {
        // 获取语言代码
        String lang = record.getLang();
        // 获取对象描述名称
        String crmObjName = record.getDescribeApiName();
        MultiLangTableEntity entity = new MultiLangTableEntity();
        entity.setId(ObjectId.get().toString());
        entity.setTenantId(tenantId);
        String biObjNameByCrmObjName = SysUdfMetadataUtil.getBiObjNameByCrmObjName(crmObjName);
        entity.setApiName(biObjNameByCrmObjName);
        entity.setCrmObjName(crmObjName);
        entity.setLang(lang);
        entity.setIsDeleted(0);
        entity.setCreatedBy("-10000");
        entity.setCreateTime(System.currentTimeMillis());
        entity.setLastModifiedTime(System.currentTimeMillis());
        entities.add(entity);
      }
      int insertedCount = multiLangTableDao.batchInsert(entities);
      log.info("insert lang obj record success, ei:{}, count:{}", tenantId, insertedCount);
    } catch (Exception e) {
      log.error("处理记录 {} 异常", tenantId, e);
    }
  }

  /**
   * 同步指定企业的指定多语言表数据
   *
   * @param tenantId   租户ID
   * @param tableNames 表名列表
   * @return 处理结果统计
   */
  public Map<String, Object> syncSpecificMultiLangTables(String tenantId, List<String> tableNames) {
    if (StringUtils.isBlank(tenantId)) {
      log.warn("租户ID为空，无法同步指定多语言表数据");
      return Collections.singletonMap("message", "租户ID为空，无法同步指定多语言表数据");
    }

    if (tableNames == null || tableNames.isEmpty()) {
      log.warn("表名列表为空，无法同步指定多语言表数据");
      return Collections.singletonMap("message", "表名列表为空，无法同步指定多语言表数据");
    }

    log.info("开始同步租户 {} 的指定多语言表数据, 表数量: {}", tenantId, tableNames.size());
    Map<String, Object> result = new HashMap<>();
    int processedTables = 0;
    int processedRecords = 0;
    int insertedFields = 0;

    try {
      // 获取schema名称
      String schemaName = "public";
      if (bipgDataSource.isSchema(tenantId)) {
        schemaName = "sch_" + tenantId;
      }

      // 查找带有多语言字段的表
      List<String> allMultiLangTables = multiLangRecordMapper.setTenantId(tenantId)
                                                             .findTablesWithMultiLangFields(schemaName);
      if (allMultiLangTables.isEmpty()) {
        log.info("租户 {} 没有带有多语言字段的表", tenantId);
        result.put("message", "没有带有多语言字段的表");
        return result;
      }

      // 过滤出指定的多语言表
      List<String> validTables = new ArrayList<>();
      for (String tableName : tableNames) {
        if ("object_data_lang".equals(tableName)) {
          tableName = "mt_data_lang";
        }
        if (allMultiLangTables.contains(tableName)) {
          validTables.add(tableName);
        } else {
          log.warn("表 {} 不是多语言表或不存在, 已跳过", tableName);
        }
      }

      if (validTables.isEmpty()) {
        log.warn("指定的表中没有有效的多语言表");
        result.put("message", "指定的表中没有有效的多语言表");
        return result;
      }

      log.info("租户 {} 有 {} 个有效的指定多语言表需要处理", tenantId, validTables.size());

      // 处理每个表
      Map<String, Object> tablesResult = new HashMap<>();
      for (String tableName : validTables) {
        try {
          Map<String, Object> tableResult = processTable(tenantId, schemaName, tableName);
          int tableProcessedRecords = (int) tableResult.getOrDefault("processedRecords", 0);
          int tableInsertedFields = (int) tableResult.getOrDefault("insertedFields", 0);

          processedRecords += tableProcessedRecords;
          insertedFields += tableInsertedFields;
          processedTables++;

          tablesResult.put(tableName, tableResult);
          log.info("处理租户 {} 表 {} 完成, 处理记录数: {}, 插入字段数: {}", tenantId, tableName, tableProcessedRecords, tableInsertedFields);
        } catch (Exception e) {
          log.error("处理租户 {} 表 {} 异常", tenantId, tableName, e);
          tablesResult.put(tableName, Collections.singletonMap("error", e.getMessage()));
        }
      }

      // 添加汇总统计
      result.put("processedTables", processedTables);
      result.put("processedRecords", processedRecords);
      result.put("insertedFields", insertedFields);
      result.put("tablesDetail", tablesResult);

      log.info("租户 {} 指定多语言表数据同步完成, 处理表数: {}, 处理记录数: {}, 插入字段数: {}", tenantId, processedTables, processedRecords, insertedFields);
      return result;
    } catch (Exception e) {
      log.error("处理租户 {} 指定多语言表异常", tenantId, e);
      result.put("error", e.getMessage());
      return result;
    }
  }
}
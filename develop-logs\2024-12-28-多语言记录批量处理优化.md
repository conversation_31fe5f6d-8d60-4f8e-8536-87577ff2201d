# 2024-12-28 多语言记录批量处理优化

## 需求描述
原有的多语言记录处理逻辑采用逐条处理方式，在MQ消息量较大时会产生大量数据库请求，影响处理性能和速度。需要将处理逻辑优化为批量处理，减少数据库请求次数，提升MQ消息处理速度。

## 修改内容

### 1. 新增批量查询接口
**文件**: `MultiLangTableInfoMapper.java` 和 `MultiLangTableInfoMapper.xml`
- 新增 `findRecordsByIds` 方法，支持批量根据ID查询记录
- 使用 MyBatis 的 `<foreach>` 标签实现 IN 查询，一次查询多个记录

### 2. 扩展历史服务批量处理能力
**文件**: `MultiLangHistoryService.java`
- 新增 `processTableByIds` 方法，支持批量处理表记录
- 新增 `BatchProcessRequest` 内部类，封装批量处理请求参数
- 使用 Stream API 进行数据转换和映射处理
- 保持原有的 `upsertLangObjRecord` 方法进行批量插入

### 3. 增强多语言服务批量处理功能
**文件**: `MultiLangService.java`
- 新增 `processMultiLangRecordsBatch` 方法，实现批量处理多语言记录
- 新增 `triggerMultiLangAggCalcBatch` 方法，批量触发多语言统计图计算
- 按表名分组进行批量处理，提高处理效率
- 增加异常处理和回退机制，确保处理的可靠性

### 4. 优化消息监听器处理逻辑
**文件**: `MultiLangRecordHandler.java`
- 修改 `onTrigger` 方法，从逐条处理改为批量处理
- 优化过滤逻辑，先过滤后批量处理
- 增加异常处理和回退机制，批量处理失败时自动回退到单条处理
- 增加调试日志，便于监控处理情况

## 技术实现要点

### 1. 批量查询优化
```java
// 使用 IN 查询替代多次单条查询
List<Map<String, Object>> records = multiLangTableInfoMapper.setTenantId(tenantId)
    .findRecordsByIds(schemaName, tableName, tenantId, primaryKeys);
```

### 2. 数据分组处理
```java
// 按表名分组，避免跨表混合处理
Map<String, List<OpLog>> tableGroupMap = opLogs.stream()
    .collect(Collectors.groupingBy(OpLog::getTable));
```

### 3. 异常处理和回退机制
```java
try {
    // 批量处理逻辑
    multiLangService.processMultiLangRecordsBatch(tenantId, validOpLogs);
} catch (Exception e) {
    // 回退到单条处理
    for (OpLog opLog : validOpLogs) {
        multiLangService.processMultiLangRecord(tenantId, opLog);
    }
}
```

## 影响范围
- **核心影响**: 多语言记录处理性能显著提升
- **数据库影响**: 减少数据库连接数和查询次数
- **MQ处理影响**: 提高消息处理吞吐量
- **系统稳定性**: 增加了回退机制，提高了系统的容错能力

## 性能优化效果
1. **数据库请求次数**: 从 N 次单条查询优化为批量查询，大幅减少数据库交互次数
2. **处理速度**: 批量处理减少了网络往返时间和数据库连接开销
3. **资源使用**: 降低数据库连接池压力和网络资源消耗
4. **扩展性**: 支持更高的并发处理能力

## 测试情况
- 保持了原有的单条处理逻辑作为回退方案
- 新增的批量处理方法与原有方法兼容
- 异常处理确保了数据处理的完整性

## 备注
1. 批量处理按表名分组，确保了数据的一致性
2. 保留了原有的灰度控制逻辑，不影响现有的开关配置
3. 增加了详细的日志记录，便于监控和调试
4. 使用了 Java 8 Stream API 和函数式编程，提高了代码的可读性和维护性 
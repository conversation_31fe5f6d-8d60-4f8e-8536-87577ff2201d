package com.fxiaoke.bi.paas2bi.transfer.mapper.bi;

import com.fxiaoke.bi.paas2bi.transfer.bean.GoalValueObjInfo;
import com.fxiaoke.bi.paas2bi.transfer.pojo.GoalValueObjCommon;
import com.github.mybatis.mapper.ICrudPaginationMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GoalValueObjMapper extends ICrudPaginationMapper<GoalValueObjInfo>, ITenant<GoalValueObjMapper> {

  @Select({"<script>",
    "select tenant_id,id,goal_rule_id,goal_rule_detail_id,rule_id,fiscal_year,month,month_value,fiscal_year_month,action_date,fiscal_action_date,oid,goal_value_name from goal_value_obj where tenant_id=#{tenantId} and oid in",
    " <foreach collection=\"oIds\" item=\"oid\" index=\"index\" open=\"(\" close=\")\" separator=\",\">",
    "#{oid}",
    "</foreach>",
    "and is_deleted=0 ",
    "</script>"})
  List<GoalValueObjInfo> queryGoalValueObjByOIds(@Param("tenantId") String tenantId,@Param("oIds") List<String> oIds);

  @Delete("delete from goal_value_obj where tenant_id=#{tenantId} and oid=#{goalValueId}")
  void deleteGoalValueObj(@Param("tenantId") String tenantId,@Param("goalValueId")String goalValueId );

  @Delete("delete from goal_value_obj where tenant_id=#{tenantId} and oid = any(array[#{goalValueIds}])")
  void batchDeleteGoalValueObj(@Param("tenantId") String tenantId,@Param("goalValueIds")String[] goalValueIds );

  @Select("SELECT * FROM #{tableName} WHERE tenant_id = #{tanantId} AND goal_rule_id = #{goalRuleId} AND is_deleted = 0")
  List<GoalValueObjCommon> getGoalValueObjByRuleId(@Param("tableName") String tableName, @Param("tenantId") String tenantId, @Param("goalRuleId") String goalRuleId);
}

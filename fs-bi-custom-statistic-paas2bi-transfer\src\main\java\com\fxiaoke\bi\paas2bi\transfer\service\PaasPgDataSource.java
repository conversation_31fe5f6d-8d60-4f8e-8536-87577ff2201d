package com.fxiaoke.bi.paas2bi.transfer.service;

import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.paas2bi.transfer.bean.OpLog;
import com.fxiaoke.common.PasswordUtil;
import com.fxiaoke.jdbc.JdbcConnection;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * Created by jief on 2021/5/18.
 */
@Slf4j
@Service
public class PaasPgDataSource {

  private String userName;
  private String passWord;
  private final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("bi-statistic-online");
  private static final String APPLICATION = "fs-bi-paas2gp-transfer";
  private static final String BIZ = "CRM";
  private static final String DIALECT = "postgresql";

  private final DbRouterClient dbRouterClient;

  public PaasPgDataSource(DbRouterClient dbRouterClient) {
    this.dbRouterClient = dbRouterClient;
  }

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-paas2gp-transfer-paasdb", this::changed);
  }

  private void changed(IConfig config) {
    this.userName = config.get("username");
    try {
      //noinspection deprecation
      this.passWord = PasswordUtil.decode(config.get("password"));
    } catch (Exception e) {
      log.error("decode error password:{}", config.get("password"), e);
    }
  }

  /**
   * 获取pg路由信息对象
   */
  public RouterInfo getRouterInfo(String tenantId, boolean usePgBouncer) {
    RouterInfo routerInfo = null;
    try {
      routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT, usePgBouncer);
    } catch (Exception e) {
      log.error("get paas routerInfo error tenantId:{}", tenantId, e);
    }
    return routerInfo;
  }

  /**
   * 获取router info
   */
  public RouterInfo getRouterInfo(String tenantId) {
    try {
      return dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT);
    } catch (Exception e) {
      log.error("can not find router info tenantId:{}", tenantId, e);
    }
    return null;
  }

  /**
   * 根据企业id获取jdbc connection
   *
   * @param tenantId 企业id
   * @return jdbcConnection
   */
  public JdbcConnection getJdbcConnectionByTenantId(String tenantId) {
    RouterInfo routerInfo = this.getRouterInfo(tenantId, gray.isAllow("use-pgbouncer-paas", tenantId));
    if (routerInfo != null) {
      String jdbcURL = routerInfo.getJdbcUrl();
      if (gray.isAllow("use-db-slave-paas", tenantId) && StringUtils.isNotBlank(routerInfo.getSlaveUrl())) {
        jdbcURL = routerInfo.getSlaveUrl();
      }
      return new JdbcConnection(jdbcURL, this.userName, this.passWord);
    }
    return null;
  }

  /**
   * 判断是否是schema隔离
   *
   * @param tenantId 企业id
   * @return boolean
   */
  public boolean isSchema(String tenantId) {
    try {
      RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT);
      return routerInfo.getStandalone();
    } catch (Exception e) {
      log.error("get paas routerInfo error tenantId:{}", tenantId, e);
    }
    return false;
  }

  /**
   * 验证同步过来的数据来源符合路由规则 *
   */
  public boolean checkRoute(OpLog changeInfo) {
    if (changeInfo != null) {
      try {
        RouterInfo routerInfo = this.getRouterInfo(changeInfo.getTenantId());
        if (null == routerInfo) {
          return true;
        }
        String masterURL = routerInfo.getJdbcUrl();
        String db_params = masterURL.substring(masterURL.lastIndexOf("/") + 1);
        String schema = "public";
        if (routerInfo.getStandalone() != null && routerInfo.getStandalone()) {
          schema = "sch_" + changeInfo.getTenantId();
        }
        String slotName = changeInfo.getDB();
        String opSchema = changeInfo.getSchema();
        if (db_params.equals(slotName) && schema.equals(opSchema)) {
          return false;
        }
        log.warn("check rout false tenant_id={},opLogSlotName:{},opLogSchema:{},reallyDB:{},reallySchema:{}", changeInfo.getTenantId(), slotName, opSchema, db_params, schema);
      } catch (Exception e) {
        log.error("check rout error tenant_id={}", changeInfo.getTenantId(), e);
      }
    }
    return true;
  }

  public JdbcConnection getJdbcConnectionByTenantID(String tenantId) {
    String pgMasterServer = getPGMasterServer(tenantId);
    return new JdbcConnection(pgMasterServer, this.userName, this.passWord);
  }

  public String getPGMasterServer(String tenantId) {
    if (gray.isAllow("use-pgbouncer", tenantId)) {
      return dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT, true).getJdbcUrl();
    }
    return dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT).getJdbcUrl();
  }
}

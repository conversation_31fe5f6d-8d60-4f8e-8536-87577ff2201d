## [2024-12-30] NewGoalValueHandler批量处理性能优化

### 需求描述
优化 `NewGoalValueHandler` 类的处理性能，将原来的单条循环处理模式改为批量处理模式，减少数据库连接开销和提高整体处理效率。

### 修改内容

#### 1. 主要优化的类和文件
- **修改类**: `fs-bi-custom-statistic-paas2bi-transfer/src/main/java/com/fxiaoke/bi/paas2bi/transfer/handler/NewGoalValueHandler.java`

#### 2. 核心优化策略

##### 2.1 批量处理架构设计
- **租户分组**: 首先按 `tenantId` 对 `OpLog` 进行分组，避免跨租户操作
- **操作类型分组**: 在每个租户内部，按操作类型（insert、update、delete）进行分组
- **优先级处理**: 优先处理删除操作，再处理插入和更新操作，避免数据冲突

##### 2.2 关键方法重构

**原来的处理逻辑**:
```java
// 单条循环处理
for (OpLog opLog : opLogs) {
  String kind = opLog.getKind();
  if (StringUtils.equalsAny(kind, "update", "insert")) {
    goalValueUpsert(opLog);  // 单条处理
  } else if ("delete".equals(kind)) {
    delete(opLog);  // 单条处理
  }
}
```

**优化后的处理逻辑**:
```java
// 按租户和操作类型分组批量处理
Map<String, List<OpLog>> tenantGroupedLogs = opLogs.stream()
    .collect(Collectors.groupingBy(OpLog::getTenantId));
tenantGroupedLogs.forEach(this::processTenantLogs);
```

##### 2.3 新增批量处理方法

1. **`processTenantLogs(String tenantId, List<OpLog> tenantOpLogs)`**
   - 处理单个租户的所有操作日志
   - 按操作类型分组并按优先级处理

2. **`batchUpsert(String tenantId, List<OpLog> upsertLogs)`**
   - 批量处理插入和更新操作
   - 过滤 touch 操作（空的 after 和 before）
   - 提取 goal_value_id 并进行批量处理

3. **`batchDelete(String tenantId, List<OpLog> deleteLogs)`**
   - 批量处理删除操作
   - 提取 goal_value_id 并进行批量删除

4. **`batchUpsertGoalValues(String tenantId, List<String> goalValueIds)`**
   - 实际执行批量 upsert 操作
   - 支持分批处理，避免单次处理数据量过大（批次大小: 50）

5. **`batchDeleteGoalValues(String tenantId, List<String> goalValueIds)`**
   - 实际执行批量删除操作
   - 支持分批处理（批次大小: 100）

##### 2.4 异常处理优化

- **容错机制**: 单条记录处理失败不影响整批次处理
- **回退策略**: 批量处理失败时，自动回退到单条处理模式
- **详细日志**: 增加详细的处理日志，便于监控和问题排查

##### 2.5 兼容性保障

- 保留原有的单条处理方法 `goalValueUpsert()` 和 `delete()`，但标记为 `@Deprecated`
- 确保向后兼容，不影响现有功能

#### 3. 性能优化收益

1. **减少数据库连接开销**: 批量处理减少了数据库连接的建立和销毁次数
2. **提高并发处理能力**: 按租户分组处理，支持不同租户间的并行处理
3. **优化资源利用**: 分批次处理避免内存压力过大
4. **增强稳定性**: 容错机制确保单条失败不影响整体处理

#### 4. 代码质量改进

- **可读性**: 方法职责更加明确，代码结构更清晰
- **可维护性**: 模块化的处理逻辑，便于后续优化和维护
- **监控友好**: 增加详细的处理日志和统计信息

### 影响范围
主要影响 `NewGoalValueHandler` 的处理性能，不影响其他模块。由于保持了接口兼容性，对调用方无影响。

### 测试情况
- 保留了原有的处理逻辑作为回退方案
- 增加了异常处理和日志记录，便于问题定位
- 建议在测试环境先验证批量处理的效果

### 备注
1. 批次大小可以根据实际业务量和服务器性能进行调整
2. 该优化主要针对大批量数据处理场景，对于小批量数据处理效果可能不明显
3. 建议监控处理时间和错误率，评估优化效果 
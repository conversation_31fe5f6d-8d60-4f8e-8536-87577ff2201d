package com.fxiaoke.bi.paas2bi.transfer.dao;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.paas2bi.transfer.areasync.utils.AreaCommonUtils;
import com.fxiaoke.bi.paas2bi.transfer.pojo.GoalValuePlus;
import com.fxiaoke.bi.paas2bi.transfer.service.PaasPgDataSource;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.mybatis.local.TenantThreadLocal;
import com.github.mybatis.mapper.ITenant;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PaasGoalRuleDao implements ITenant<PaasGoalRuleDao> {

  @Resource
  private PaasPgDataSource paasPgDataSource;

  private static final String queryGoalRuleSQL =
    "select t1.tenant_id,t1.id,t1.name,t1.check_object_api_name,t1.start_month,t1.start_week,t1.start_quarter,t1.check_level_field_api_name as check_level_field_api_names,t1.check_level_type, t1.check_cycle, t1.check_dimension_fields as rule_check_dimension_fields " +
    " from %s.goal_rule t1 where t1.tenant_id=? and t1.id=any(array[?]) AND t1.is_deleted=0";

  private static final String QUERY_GOAL_RULE_SQL = "SELECT t0.*, t1.start_month, t1.start_week, t1.start_quarter, t1.check_level_field_api_name AS check_level_field_api_names, t1.check_level_type, t1.check_cycle, t1.check_dimension_fields AS rule_check_dimension_fields FROM goal_value t0 LEFT JOIN goal_rule t1 ON t0.tenant_id = t1.tenant_id AND t0.goal_rule_id = t1.ID AND t1.tenant_id = ? WHERE t0.tenant_id = ? AND t1.id = ? AND t0.id > ? ORDER BY t0.id ASC LIMIT ?";

  /**
   * 批量查询目标规则基本信息
   */
  public List<GoalRuleCheckLevelDO> batchQueryPaasGoalRuleByIds(Set<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return null;
    }
    String[] idArray = ids.toArray(new String[0]);
    String tenantId = TenantThreadLocal.get();
    String schemaName = "public";
    if (paasPgDataSource.isSchema(tenantId)) {
      schemaName = "sch_" + tenantId;
    }
    List<GoalRuleCheckLevelDO> result = Lists.newArrayList();
    try (JdbcConnection jdbcConnection = paasPgDataSource.getJdbcConnectionByTenantId(tenantId)) {
      String sql = String.format(queryGoalRuleSQL, schemaName);
      jdbcConnection.prepareQuery(sql, preparedStatement -> {
        preparedStatement.setString(1, tenantId);
        preparedStatement.setObject(2, idArray);
      }, resultSet -> {
        while (resultSet.next()) {
          GoalRuleCheckLevelDO goalRuleCheckLevelDO = GoalRuleCheckLevelDO.builder()
                                                                          .id(resultSet.getString("id"))
                                                                          .tenantId(resultSet.getString("tenant_id"))
                                                                          .startMonth(resultSet.getString("start_month"))
                                                                          .startWeek(resultSet.getString("start_week"))
                                                                          .startQuarter(resultSet.getString("start_quarter"))
                                                                          .checkLevelFieldApiNames(resultSet.getString("check_level_field_api_names"))
                                                                          .checkLevelType(resultSet.getString("check_level_type"))
                                                                          .checkCycle(resultSet.getString("check_cycle"))
                                                                          .name(resultSet.getString("name"))
                                                                          .checkObjectApiName(resultSet.getString("check_object_api_name"))
                                                                          .ruleCheckDimensionFields(resultSet.getString("rule_check_dimension_fields"))
                                                                          .build();
          result.add(goalRuleCheckLevelDO);
        }
      });
    } catch (Exception e) {
      log.error("query goalRule from paas db error tenantId:{},ruleids:{}", tenantId, JSON.toJSONString(ids), e);
      throw new RuntimeException(e);
    }
    return result;
  }

  /**
   * 根据目标id查询所有paas 目标规则的基本信息
   */
  public Map<String, GoalRuleCheckLevelDO> findPaasGoalRuleByIds(Set<String> ids) {
    List<GoalRuleCheckLevelDO> result = this.batchQueryPaasGoalRuleByIds(ids);
    if (CollectionUtils.isNotEmpty(result)) {
      return result.stream().collect(Collectors.toMap(GoalRuleCheckLevelDO::getId, Function.identity()));
    }
    return null;
  }

  public List<GoalValuePlus> batchQueryGoalValueByGoalRuleId(String tenantId,
                                                             String goalRuleId,
                                                             String lastFlag,
                                                             int pageSize) {
    JdbcConnection jdbcConnection = paasPgDataSource.getJdbcConnectionByTenantId(tenantId);
    return AreaCommonUtils.queryMany(QUERY_GOAL_RULE_SQL, jdbcConnection, GoalValuePlus.class, tenantId, tenantId, goalRuleId, lastFlag, pageSize);
  }


  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class GoalRuleCheckLevelDO {
    private String tenantId;
    private String id;
    private String startMonth;
    private String startWeek;
    private String startQuarter;
    private String checkLevelFieldApiNames;
    private String checkLevelType;
    private String checkCycle;
    private String name;
    private String checkObjectApiName;
    private String ruleCheckDimensionFields;

    @Override
    public boolean equals(Object o) {
      if (this == o) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      GoalRuleCheckLevelDO that = (GoalRuleCheckLevelDO) o;
      return tenantId.equals(that.tenantId) && id.equals(that.id);
    }

    @Override
    public int hashCode() {
      return Objects.hash(tenantId, id);
    }
  }
}

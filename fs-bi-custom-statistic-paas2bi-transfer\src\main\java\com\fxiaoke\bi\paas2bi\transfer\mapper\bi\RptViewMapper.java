package com.fxiaoke.bi.paas2bi.transfer.mapper.bi;

import com.fxiaoke.bi.paas2bi.transfer.bean.RptViewEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RptViewMapper extends ITenant<RptViewMapper> {

    @Select("<script>" +
            "SELECT ei, view_id, is_delete " +
            "FROM rpt_view " +
            "WHERE ei = #{ei} " +
            "AND view_id IN " +
            "<foreach collection='viewIds' item='viewId' open='(' separator=',' close=')'>" +
            "#{viewId}" +
            "</foreach>" +
            "</script>")
    List<RptViewEntity> batchQueryRptViewStatus(@Param("ei") Integer ei, @Param("viewIds") List<String> viewIds);

} 